<?php

namespace App\Console\Commands;

use App\Models\Module;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class RegisterModule extends Command
{
    protected $signature = 'module:register {slug?}';
    protected $description = 'Register a module from the modules directory';

    public function handle()
    {
        $slug = $this->argument('slug');
        $modulesPath = base_path('modules');

        if (!File::exists($modulesPath)) {
            $this->error('Modules directory does not exist.');
            return 1;
        }

        if ($slug) {
            $this->registerModule($slug);
        } else {
            $directories = File::directories($modulesPath);
            foreach ($directories as $directory) {
                $slug = basename($directory);
                $this->registerModule($slug);
            }
        }

        return 0;
    }

    protected function registerModule($slug)
    {
        $modulePath = base_path("modules/{$slug}");
        $moduleJsonPath = "{$modulePath}/module.json";

        if (!File::exists($moduleJsonPath)) {
            $this->error("Module {$slug} does not have a module.json file.");
            return;
        }

        $moduleData = json_decode(File::get($moduleJsonPath), true);

        if (!$moduleData) {
            $this->error("Invalid module.json file for module {$slug}.");
            return;
        }

        Module::updateOrCreate(
            ['slug' => $slug],
            [
                'name' => $moduleData['name'],
                'version' => $moduleData['version'],
                'description' => $moduleData['description'] ?? null,
                'author' => $moduleData['author'] ?? null,
                'author_url' => $moduleData['author_url'] ?? null,
                'settings' => $moduleData['settings'] ?? null,
            ]
        );

        $this->info("Module {$slug} registered successfully.");
    }
} 