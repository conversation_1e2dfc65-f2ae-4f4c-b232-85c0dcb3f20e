<?php

namespace App\Services;

use App\Models\OtpSetting;
use App\Models\OtpTemplate;
use App\Models\SmtpSetting;
use App\Models\User;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class OtpService
{
    protected $otpSettings;
    protected $smtpSettings;

    public function __construct()
    {
        $this->otpSettings = OtpSetting::first();
        $this->smtpSettings = SmtpSetting::where('is_active', true)->first();
    }

    public function generateOtp(User $user, string $type): string
    {
        $otp = Str::random(6);
        
        // Store OTP in cache with expiry
        $key = "otp:{$user->id}:{$type}";
        cache()->put($key, $otp, now()->addMinutes($this->otpSettings->otp_expiry_minutes));
        
        return $otp;
    }

    public function verifyOtp(User $user, string $type, string $otp): bool
    {
        $key = "otp:{$user->id}:{$type}";
        $storedOtp = cache()->get($key);
        
        if (!$storedOtp) {
            return false;
        }
        
        if ($storedOtp === $otp) {
            cache()->forget($key);
            return true;
        }
        
        return false;
    }

    public function sendOtp(User $user, string $type): bool
    {
        if (!$this->isOtpEnabled($type)) {
            return false;
        }

        $otp = $this->generateOtp($user, $type);
        $template = $this->getTemplate($type);
        
        if (!$template || !$template->is_active) {
            return false;
        }

        $data = [
            'name' => $user->name,
            'email' => $user->email,
            'phone' => $user->phone,
            'otp' => $otp,
            'expiry_time' => $this->otpSettings->otp_expiry_minutes,
        ];

        switch ($type) {
            case 'email':
                return $this->sendEmailOtp($user, $template, $data);
            case 'sms':
                return $this->sendSmsOtp($user, $template, $data);
            case 'whatsapp':
                return $this->sendWhatsappOtp($user, $template, $data);
            case 'telegram':
                return $this->sendTelegramOtp($user, $template, $data);
            default:
                return false;
        }
    }

    protected function isOtpEnabled(string $type): bool
    {
        return match ($type) {
            'email' => $this->otpSettings->email_otp_enabled,
            'phone' => $this->otpSettings->phone_otp_enabled,
            'whatsapp' => $this->otpSettings->whatsapp_otp_enabled,
            'telegram' => $this->otpSettings->telegram_otp_enabled,
            default => false,
        };
    }

    protected function getTemplate(string $type): ?OtpTemplate
    {
        return OtpTemplate::where('type', $type)
            ->where('is_active', true)
            ->first();
    }

    protected function sendEmailOtp(User $user, OtpTemplate $template, array $data): bool
    {
        if (!$this->smtpSettings || !$this->smtpSettings->is_active) {
            return false;
        }

        try {
            config([
                'mail.mailers.smtp.host' => $this->smtpSettings->host,
                'mail.mailers.smtp.port' => $this->smtpSettings->port,
                'mail.mailers.smtp.username' => $this->smtpSettings->username,
                'mail.mailers.smtp.password' => $this->smtpSettings->password,
                'mail.mailers.smtp.encryption' => $this->smtpSettings->encryption,
                'mail.from.address' => $this->smtpSettings->from_address,
                'mail.from.name' => $this->smtpSettings->from_name,
            ]);

            $body = $this->replaceVariables($template->body, $data);
            
            Mail::raw($body, function ($message) use ($user, $template, $data) {
                $message->to($user->email)
                    ->subject($this->replaceVariables($template->subject, $data));
            });

            return true;
        } catch (\Exception $e) {
            report($e);
            return false;
        }
    }

    protected function sendSmsOtp(User $user, OtpTemplate $template, array $data): bool
    {
        // Implement SMS sending logic here
        // This will depend on your SMS provider
        return false;
    }

    protected function sendWhatsappOtp(User $user, OtpTemplate $template, array $data): bool
    {
        // Implement WhatsApp sending logic here
        // This will depend on your WhatsApp provider
        return false;
    }

    protected function sendTelegramOtp(User $user, OtpTemplate $template, array $data): bool
    {
        // Implement Telegram sending logic here
        // This will depend on your Telegram bot setup
        return false;
    }

    protected function replaceVariables(string $content, array $data): string
    {
        foreach ($data as $key => $value) {
            $content = str_replace("{{$key}}", $value, $content);
        }
        return $content;
    }
} 