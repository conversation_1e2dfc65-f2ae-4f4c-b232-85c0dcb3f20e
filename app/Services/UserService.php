<?php

namespace App\Services;

use App\Models\User;
use App\Models\Role;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

class UserService
{
    public function createUser($data, $parent = null)
    {
        return DB::transaction(function () use ($data, $parent) {
            // Get role
            $role = Role::where('slug', $data['role'])->first();
            if (!$role) {
                throw new \Exception('Invalid role');
            }

            // Validate parent can create this role
            if ($parent && !$parent->canCreateUser($data['role'])) {
                throw new \Exception('Parent user cannot create users with this role');
            }

            // Create user
            $user = User::create([
                'name' => $data['name'],
                'email' => $data['email'],
                'password' => Hash::make($data['password']),
                'role_id' => $role->id,
                'parent_id' => $parent ? $parent->id : null,
                'phone' => $data['phone'] ?? null,
                'whatsapp' => $data['whatsapp'] ?? null,
                'telegram' => $data['telegram'] ?? null,
                'is_active' => true
            ]);

            return $user;
        });
    }

    public function updateUser(User $user, $data)
    {
        return DB::transaction(function () use ($user, $data) {
            $user->name = $data['name'] ?? $user->name;
            $user->email = $data['email'] ?? $user->email;
            $user->phone = $data['phone'] ?? $user->phone;
            $user->whatsapp = $data['whatsapp'] ?? $user->whatsapp;
            $user->telegram = $data['telegram'] ?? $user->telegram;
            $user->is_active = $data['is_active'] ?? $user->is_active;

            if (isset($data['password'])) {
                $user->password = Hash::make($data['password']);
            }

            $user->save();
            return $user;
        });
    }

    public function getHierarchy(User $user)
    {
        $hierarchy = [];
        $currentUser = $user;

        while ($currentUser->parent) {
            $hierarchy[] = $currentUser->parent;
            $currentUser = $currentUser->parent;
        }

        return array_reverse($hierarchy);
    }

    public function getChildren(User $user, $role = null)
    {
        $query = $user->children();

        if ($role) {
            $query->whereHas('role', function ($q) use ($role) {
                $q->where('slug', $role);
            });
        }

        return $query->get();
    }

    public function getAllChildren(User $user)
    {
        $children = collect();
        $this->getAllChildrenRecursive($user, $children);
        return $children;
    }

    protected function getAllChildrenRecursive(User $user, &$children)
    {
        $directChildren = $user->children;
        $children = $children->concat($directChildren);

        foreach ($directChildren as $child) {
            $this->getAllChildrenRecursive($child, $children);
        }
    }
} 