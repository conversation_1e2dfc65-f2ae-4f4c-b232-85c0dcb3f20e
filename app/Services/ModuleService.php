<?php

namespace App\Services;

use App\Models\Module;
use Illuminate\Support\Facades\Storage;
use ZipArchive;

class ModuleService
{
    public function install($zipFile)
    {
        // Extract module information
        $moduleInfo = $this->extractModuleInfo($zipFile);
        if (!$moduleInfo) {
            throw new \Exception('Invalid module package');
        }

        // Check if module already exists
        $existingModule = Module::where('slug', $moduleInfo['slug'])->first();
        if ($existingModule) {
            throw new \Exception('Module already exists');
        }

        // Create module directory
        $modulePath = storage_path("app/modules/{$moduleInfo['slug']}");
        if (!file_exists($modulePath)) {
            mkdir($modulePath, 0755, true);
        }

        // Extract module files
        $zip = new ZipArchive;
        if ($zip->open($zipFile) === true) {
            $zip->extractTo($modulePath);
            $zip->close();
        } else {
            throw new \Exception('Failed to extract module package');
        }

        // Create module record
        $module = Module::create([
            'name' => $moduleInfo['name'],
            'slug' => $moduleInfo['slug'],
            'version' => $moduleInfo['version'],
            'description' => $moduleInfo['description'] ?? null,
            'author' => $moduleInfo['author'] ?? null,
            'author_email' => $moduleInfo['author_email'] ?? null,
            'author_url' => $moduleInfo['author_url'] ?? null,
            'dependencies' => $moduleInfo['dependencies'] ?? [],
            'settings' => $moduleInfo['settings'] ?? [],
            'is_active' => false
        ]);

        return $module;
    }

    public function uninstall($moduleSlug)
    {
        $module = Module::where('slug', $moduleSlug)->first();
        if (!$module) {
            throw new \Exception('Module not found');
        }

        // Uninstall module
        $module->uninstall();

        // Remove module files
        $modulePath = storage_path("app/modules/{$moduleSlug}");
        if (file_exists($modulePath)) {
            $this->removeDirectory($modulePath);
        }

        return true;
    }

    protected function extractModuleInfo($zipFile)
    {
        $zip = new ZipArchive;
        if ($zip->open($zipFile) === true) {
            $infoFile = $zip->getFromName('module.json');
            $zip->close();

            if ($infoFile) {
                return json_decode($infoFile, true);
            }
        }

        return null;
    }

    protected function removeDirectory($dir)
    {
        if (!file_exists($dir)) {
            return true;
        }

        if (!is_dir($dir)) {
            return unlink($dir);
        }

        foreach (scandir($dir) as $item) {
            if ($item == '.' || $item == '..') {
                continue;
            }

            if (!$this->removeDirectory($dir . DIRECTORY_SEPARATOR . $item)) {
                return false;
            }
        }

        return rmdir($dir);
    }
} 