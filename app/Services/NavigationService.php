<?php

namespace App\Services;

use App\Models\User;
use App\Models\PagePermission;

class NavigationService
{
    public static function getNavigationItems(User $user)
    {
        $userRole = $user->role->slug;
        $userPermissions = $user->getUserPermissions();

        $allNavItems = [
            [
                'title' => 'Dashboard',
                'href' => '/dashboard',
                'icon' => 'LayoutGrid',
                'required_permissions' => ['view-dashboard'],
                'required_roles' => []
            ],
            [
                'title' => 'User Management',
                'href' => '/users',
                'icon' => 'Users',
                'required_permissions' => ['view-users'],
                'required_roles' => []
            ],
            [
                'title' => 'Commission Management',
                'href' => '/commissions',
                'icon' => 'DollarSign',
                'required_permissions' => ['view-commissions'],
                'required_roles' => []
            ],
            [
                'title' => 'Module Management',
                'href' => '/admin/modules',
                'icon' => 'Package',
                'required_permissions' => ['view-modules'],
                'required_roles' => []
            ],
            [
                'title' => 'Settings',
                'href' => '/settings',
                'icon' => 'Settings',
                'required_permissions' => ['view-settings'],
                'required_roles' => []
            ],
            [
                'title' => 'OTP Settings',
                'href' => '/admin/settings/otp',
                'icon' => 'ShieldCheck',
                'required_permissions' => ['manage-otp-settings'],
                'required_roles' => []
            ],
            [
                'title' => 'Role Management',
                'href' => '/admin/roles',
                'icon' => 'Shield',
                'required_permissions' => [],
                'required_roles' => ['super-admin']
            ],
            [
                'title' => 'Page Permissions',
                'href' => '/admin/page-permissions',
                'icon' => 'Lock',
                'required_permissions' => [],
                'required_roles' => ['super-admin']
            ]
        ];

        $allowedNavItems = [];

        foreach ($allNavItems as $item) {
            $hasAccess = true;

            // Check role-based access first (takes precedence)
            if (!empty($item['required_roles'])) {
                $hasAccess = in_array($userRole, $item['required_roles']);
            }
            // Check permission-based access if no role restriction
            elseif (!empty($item['required_permissions'])) {
                $hasAccess = false;
                foreach ($item['required_permissions'] as $permission) {
                    if (in_array($permission, $userPermissions)) {
                        $hasAccess = true;
                        break;
                    }
                }
            }

            if ($hasAccess) {
                // Remove the access control fields before sending to frontend
                unset($item['required_permissions']);
                unset($item['required_roles']);
                $allowedNavItems[] = $item;
            }
        }

        return $allowedNavItems;
    }

    public static function getFooterNavItems()
    {
        return [
            [
                'title' => 'Repository',
                'href' => 'https://github.com/laravel/react-starter-kit',
                'icon' => 'Folder'
            ],
            [
                'title' => 'Documentation',
                'href' => 'https://laravel.com/docs/starter-kits#react',
                'icon' => 'BookOpen'
            ]
        ];
    }
}
