<?php

namespace App\Services;

use App\Models\Transaction;
use App\Models\User;
use App\Models\Module;
use Illuminate\Support\Facades\DB;

class TransactionService
{
    public function createTransaction($user, $amount, $type, $module = null, $referenceId = null, $description = null, $metadata = [])
    {
        return DB::transaction(function () use ($user, $amount, $type, $module, $referenceId, $description, $metadata) {
            $transaction = Transaction::createTransaction(
                $user,
                $amount,
                $type,
                $module,
                $referenceId,
                $description,
                $metadata
            );

            if ($type === 'credit' && $module) {
                $transaction->processCommissions();
            }

            return $transaction;
        });
    }

    public function syncUserBalance(User $user)
    {
        $user->syncBalance();
        return $user->balance;
    }

    public function validateBalance(User $user)
    {
        $calculatedBalance = $user->transactions()
            ->where('status', 'completed')
            ->sum(DB::raw("CASE WHEN type = 'credit' THEN amount ELSE -amount END"));

        if ($calculatedBalance != $user->balance) {
            $user->balance = $calculatedBalance;
            $user->save();
            return false;
        }

        return true;
    }

    public function getTransactionHistory(User $user, $filters = [])
    {
        $query = $user->transactions()
            ->with(['module'])
            ->orderBy('created_at', 'desc');

        if (isset($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        if (isset($filters['module_id'])) {
            $query->where('module_id', $filters['module_id']);
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['start_date'])) {
            $query->where('created_at', '>=', $filters['start_date']);
        }

        if (isset($filters['end_date'])) {
            $query->where('created_at', '<=', $filters['end_date']);
        }

        return $query->paginate($filters['per_page'] ?? 15);
    }
} 