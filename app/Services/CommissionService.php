<?php

namespace App\Services;

use App\Models\Commission;
use App\Models\User;
use App\Models\Module;
use Illuminate\Support\Facades\DB;

class CommissionService
{
    public function setCommission(User $user, Module $module, $percentage)
    {
        return DB::transaction(function () use ($user, $module, $percentage) {
            $commission = Commission::updateOrCreate(
                [
                    'user_id' => $user->id,
                    'module_id' => $module->id
                ],
                [
                    'percentage' => $percentage,
                    'is_active' => true
                ]
            );

            return $commission;
        });
    }

    public function removeCommission(User $user, Module $module)
    {
        return Commission::where('user_id', $user->id)
            ->where('module_id', $module->id)
            ->delete();
    }

    public function getCommission(User $user, Module $module)
    {
        return Commission::where('user_id', $user->id)
            ->where('module_id', $module->id)
            ->where('is_active', true)
            ->first();
    }

    public function getCommissions(User $user)
    {
        return Commission::where('user_id', $user->id)
            ->where('is_active', true)
            ->with('module')
            ->get();
    }

    public function calculateCommission(User $user, Module $module, $amount)
    {
        $commission = $this->getCommission($user, $module);
        if (!$commission) {
            return 0;
        }

        return $commission->calculateCommission($amount);
    }

    public function getCommissionHierarchy(User $user, Module $module)
    {
        $hierarchy = [];
        $currentUser = $user;

        while ($currentUser->parent) {
            $currentUser = $currentUser->parent;
            $commission = $this->getCommission($currentUser, $module);
            
            if ($commission) {
                $hierarchy[] = [
                    'user' => $currentUser,
                    'commission' => $commission
                ];
            }
        }

        return $hierarchy;
    }
} 