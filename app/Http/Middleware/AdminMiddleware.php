<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!$request->user()) {
            abort(401, 'Unauthorized.');
        }

        $user = $request->user();
        $allowedRoles = ['super-admin', 'admin'];

        if (!in_array($user->role->slug, $allowedRoles)) {
            abort(403, 'Access denied. Admin access required.');
        }

        return $next($request);
    }
}