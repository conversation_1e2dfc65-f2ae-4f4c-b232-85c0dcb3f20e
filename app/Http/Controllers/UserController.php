<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Role;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Inertia\Inertia;
use Inertia\Response;

class UserController extends Controller
{
    /**
     * Display a listing of users.
     */
    public function index(): Response
    {
        $user = auth()->user();
        
        // If user is super-admin, show all users
        if ($user->hasRole('super-admin')) {
            $users = User::with(['role', 'parent'])
                ->orderBy('created_at', 'desc')
                ->paginate(10);
        } else {
            // Show only users created by the current user and their own profile
            $users = User::with(['role', 'parent'])
                ->where(function($query) use ($user) {
                    $query->where('parent_id', $user->id)
                          ->orWhere('id', $user->id);
                })
                ->orderBy('created_at', 'desc')
                ->paginate(10);
        }

        return Inertia::render('users/index', [
            'users' => $users,
        ]);
    }

    /**
     * Show the form for creating a new user.
     */
    public function create(): Response
    {
        $user = auth()->user();
        $roles = Role::whereIn('slug', $user->canCreateUser())
            ->get();
            
        return Inertia::render('users/create', [
            'roles' => $roles,
        ]);
    }

    /**
     * Store a newly created user in storage.
     */
    public function store(Request $request)
    {
        $user = auth()->user();
        
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'role_id' => 'required|exists:roles,id',
        ]);

        $role = Role::findOrFail($request->role_id);
        
        if (!$user->canCreateUser($role->slug)) {
            return back()->with('error', 'You do not have permission to create users with this role.');
        }

        $newUser = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role_id' => $request->role_id,
            'parent_id' => $user->id,
        ]);

        return redirect()->route('users.index')
            ->with('success', 'User created successfully.');
    }

    /**
     * Show the form for editing the specified user.
     */
    public function edit(User $user): Response
    {
        $currentUser = auth()->user();
        
        // Check if user can edit this user
        if (!$currentUser->canEditUser($user)) {
            abort(403, 'You do not have permission to edit this user.');
        }

        $roles = Role::whereIn('slug', $currentUser->canCreateUser())
            ->get();
            
        return Inertia::render('users/edit', [
            'user' => $user->load('role'),
            'roles' => $roles,
        ]);
    }

    /**
     * Update the specified user in storage.
     */
    public function update(Request $request, User $user)
    {
        $currentUser = auth()->user();
        
        // Check if user can edit this user
        if (!$currentUser->canEditUser($user)) {
            abort(403, 'You do not have permission to edit this user.');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'role_id' => 'required|exists:roles,id',
        ]);

        $role = Role::findOrFail($request->role_id);
        
        // Check if user can assign this role
        if (!$currentUser->canCreateUser($role->slug)) {
            return back()->with('error', 'You do not have permission to assign this role.');
        }

        $user->update([
            'name' => $request->name,
            'email' => $request->email,
            'role_id' => $request->role_id,
        ]);

        if ($request->filled('password')) {
            $request->validate([
                'password' => ['required', 'confirmed', Rules\Password::defaults()],
            ]);

            $user->update([
                'password' => Hash::make($request->password),
            ]);
        }

        return redirect()->route('users.index')
            ->with('success', 'User updated successfully.');
    }

    /**
     * Remove the specified user from storage.
     */
    public function destroy(User $user)
    {
        if ($user->id === auth()->id()) {
            return back()->with('error', 'You cannot delete your own account.');
        }

        $user->delete();

        return back()->with('success', 'User deleted successfully.');
    }
} 