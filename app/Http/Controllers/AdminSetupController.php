<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Role;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Inertia\Inertia;

class AdminSetupController extends Controller
{
    public function create()
    {
        // Check if any admin exists
        $adminRole = Role::where('slug', 'admin')->first();
        $adminExists = User::where('role_id', $adminRole->id)->exists();

        if ($adminExists) {
            return redirect()->route('home');
        }

        return Inertia::render('admin-setup');
    }

    public function store()
    {
        // Check if any admin exists
        $adminRole = Role::where('slug', 'admin')->first();
        $adminExists = User::where('role_id', $adminRole->id)->exists();

        if ($adminExists) {
            return redirect()->route('home');
        }

        // Generate a random password
        $password = Str::random(12);

        // Create admin user
        $user = User::create([
            'name' => 'System Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make($password),
            'role_id' => $adminRole->id,
        ]);

        return Inertia::render('admin-setup', [
            'adminCreated' => true,
            'credentials' => [
                'email' => '<EMAIL>',
                'password' => $password
            ]
        ]);
    }
} 