<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\OtpSetting;
use App\Models\OtpTemplate;
use App\Models\SmtpSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class OtpSettingsController extends Controller
{
    public function index()
    {
        $otpSettings = OtpSetting::first() ?? new OtpSetting();
        $otpTemplates = OtpTemplate::all();
        $smtpSettings = SmtpSetting::first() ?? new SmtpSetting();

        return inertia('settings/otp/index', [
            'otpSettings' => $otpSettings,
            'otpTemplates' => $otpTemplates,
            'smtpSettings' => $smtpSettings,
        ]);
    }

    public function updateOtpSettings(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email_otp_enabled' => 'boolean',
            'phone_otp_enabled' => 'boolean',
            'whatsapp_otp_enabled' => 'boolean',
            'telegram_otp_enabled' => 'boolean',
            'email_verification_required' => 'boolean',
            'phone_verification_required' => 'boolean',
            'whatsapp_verification_required' => 'boolean',
            'telegram_verification_required' => 'boolean',
            'otp_expiry_minutes' => 'required|integer|min:1|max:60',
            'max_otp_attempts' => 'required|integer|min:1|max:10',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $otpSettings = OtpSetting::first() ?? new OtpSetting();
        $otpSettings->fill($request->all());
        $otpSettings->save();

        return back()->with('success', 'OTP settings updated successfully');
    }

    public function updateTemplate(Request $request, OtpTemplate $template)
    {
        $validator = Validator::make($request->all(), [
            'subject' => 'nullable|string|max:255',
            'body' => 'required|string',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $template->update($request->all());

        return back()->with('success', 'Template updated successfully');
    }

    public function updateSmtpSettings(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'mailer' => 'required|string',
            'host' => 'required|string',
            'port' => 'required|integer',
            'username' => 'required|string',
            'password' => 'required|string',
            'encryption' => 'required|string',
            'from_address' => 'required|email',
            'from_name' => 'required|string',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $smtpSettings = SmtpSetting::first() ?? new SmtpSetting();
        $smtpSettings->fill($request->all());
        $smtpSettings->save();

        return back()->with('success', 'SMTP settings updated successfully');
    }

    public function testSmtpConnection(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'host' => 'required|string',
            'port' => 'required|integer',
            'username' => 'required|string',
            'password' => 'required|string',
            'encryption' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'message' => 'Invalid input']);
        }

        try {
            $transport = new \Swift_SmtpTransport(
                $request->host,
                $request->port,
                $request->encryption
            );
            $transport->setUsername($request->username);
            $transport->setPassword($request->password);

            $mailer = new \Swift_Mailer($transport);
            $mailer->getTransport()->start();

            return response()->json(['success' => true, 'message' => 'SMTP connection successful']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    public function createDefaultTemplates()
    {
        try {
            \Log::info('Starting to create default OTP templates');
            
            $seeder = new \Database\Seeders\OtpTemplateSeeder();
            $seeder->run();
            
            \Log::info('Successfully created default OTP templates');
            return back()->with('success', 'Default templates created successfully');
        } catch (\Exception $e) {
            \Log::error('Failed to create default OTP templates: ' . $e->getMessage());
            return back()->with('error', 'Failed to create default templates: ' . $e->getMessage());
        }
    }
} 