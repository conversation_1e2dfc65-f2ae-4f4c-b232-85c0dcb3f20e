<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Module;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use ZipArchive;

class ModuleController extends Controller
{
    public function index()
    {
        $modules = Module::all();
        return inertia('admin/modules/index', [
            'modules' => $modules
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'module' => 'required|file|mimes:zip'
        ]);

        $zipFile = $request->file('module');
        $zipPath = $zipFile->getRealPath();
        
        // Create modules directory if it doesn't exist
        if (!File::exists(base_path('modules'))) {
            File::makeDirectory(base_path('modules'), 0755, true);
        }

        $zip = new ZipArchive;
        if ($zip->open($zipPath) === TRUE) {
            // Extract to a temporary directory first
            $tempPath = storage_path('app/temp/' . Str::random(10));
            $zip->extractTo($tempPath);
            $zip->close();

            // Read module.json
            $moduleJson = json_decode(File::get($tempPath . '/module.json'), true);
            
            if (!$moduleJson) {
                File::deleteDirectory($tempPath);
                return back()->with('error', 'Invalid module package. Missing module.json');
            }

            // Validate required fields
            $requiredFields = ['name', 'slug', 'version'];
            foreach ($requiredFields as $field) {
                if (!isset($moduleJson[$field])) {
                    File::deleteDirectory($tempPath);
                    return back()->with('error', "Invalid module package. Missing required field: {$field}");
                }
            }

            // Move to final location
            $modulePath = base_path('modules/' . $moduleJson['slug']);
            if (File::exists($modulePath)) {
                File::deleteDirectory($modulePath);
            }
            File::moveDirectory($tempPath, $modulePath);

            // Create or update module record
            Module::updateOrCreate(
                ['slug' => $moduleJson['slug']],
                [
                    'name' => $moduleJson['name'],
                    'version' => $moduleJson['version'],
                    'description' => $moduleJson['description'] ?? null,
                    'author' => $moduleJson['author'] ?? null,
                    'author_url' => $moduleJson['author_url'] ?? null,
                    'settings' => $moduleJson['settings'] ?? null,
                ]
            );

            return back()->with('success', 'Module uploaded successfully');
        }

        return back()->with('error', 'Failed to extract module package');
    }

    public function activate(Module $module)
    {
        // Run migrations
        $migrationPath = $module->getMigrationPath();
        if (File::exists($migrationPath)) {
            $this->runMigrations($migrationPath);
        }

        $module->update(['is_active' => true]);
        return back()->with('success', 'Module activated successfully');
    }

    public function deactivate(Module $module)
    {
        // Rollback migrations to remove module tables
        $migrationPath = $module->getMigrationPath();
        if (File::exists($migrationPath)) {
            $this->rollbackMigrations($migrationPath);
        }

        // Update module status
        $module->update(['is_active' => false]);
        return back()->with('success', 'Module deactivated successfully');
    }

    public function destroy(Module $module)
    {
        // Drop module tables
        $migrationPath = $module->getMigrationPath();
        if (File::exists($migrationPath)) {
            $this->rollbackMigrations($migrationPath);
        }

        // Delete module files
        File::deleteDirectory($module->getModulePath());

        // Delete module record
        $module->delete();

        return back()->with('success', 'Module deleted successfully');
    }

    public function refresh()
    {
        $modulesPath = base_path('modules');
        if (!File::exists($modulesPath)) {
            return back()->with('error', 'Modules directory does not exist.');
        }

        $directories = File::directories($modulesPath);
        $registeredCount = 0;

        foreach ($directories as $directory) {
            $slug = basename($directory);
            $moduleJsonPath = "{$directory}/module.json";

            if (!File::exists($moduleJsonPath)) {
                continue;
            }

            $moduleData = json_decode(File::get($moduleJsonPath), true);
            if (!$moduleData) {
                continue;
            }

            Module::updateOrCreate(
                ['slug' => $slug],
                [
                    'name' => $moduleData['name'],
                    'version' => $moduleData['version'],
                    'description' => $moduleData['description'] ?? null,
                    'author' => $moduleData['author'] ?? null,
                    'author_url' => $moduleData['author_url'] ?? null,
                    'settings' => $moduleData['settings'] ?? null,
                ]
            );

            $registeredCount++;
        }

        return back()->with('success', "Successfully refreshed {$registeredCount} modules.");
    }

    protected function runMigrations($path)
    {
        $migrations = File::glob($path . '/*.php');
        foreach ($migrations as $migration) {
            require_once $migration;
            
            // For anonymous classes, we need to execute the file directly
            if (preg_match('/return\s+new\s+class\s+extends\s+Migration/', File::get($migration))) {
                $migration = require $migration;
                $migration->up();
            } else {
                // For named classes, use the traditional approach
                $class = $this->getMigrationClass($migration);
                (new $class)->up();
            }
        }
    }

    protected function rollbackMigrations($path)
    {
        $migrations = File::glob($path . '/*.php');
        foreach (array_reverse($migrations) as $migration) {
            require_once $migration;
            
            // For anonymous classes, we need to execute the file directly
            if (preg_match('/return\s+new\s+class\s+extends\s+Migration/', File::get($migration))) {
                $migration = require $migration;
                $migration->down();
            } else {
                // For named classes, use the traditional approach
                $class = $this->getMigrationClass($migration);
                (new $class)->down();
            }
        }
    }

    protected function getMigrationClass($path)
    {
        $file = File::get($path);
        
        // Only look for named classes
        if (preg_match('/class\s+(\w+)\s+extends\s+Migration/', $file, $matches)) {
            return $matches[1];
        }
        
        throw new \RuntimeException("Could not find named migration class in file: {$path}");
    }
} 