<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\PagePermission;
use App\Models\Permission;
use App\Models\Role;
use Illuminate\Http\Request;
use Inertia\Inertia;

class PagePermissionController extends Controller
{
    public function index()
    {
        $pagePermissions = PagePermission::all();
        $permissions = Permission::all();
        $roles = Role::all();

        return Inertia::render('admin/page-permissions/index', [
            'pagePermissions' => $pagePermissions,
            'permissions' => $permissions,
            'roles' => $roles
        ]);
    }

    public function create()
    {
        $permissions = Permission::all();
        $roles = Role::all();

        return Inertia::render('admin/page-permissions/create', [
            'permissions' => $permissions,
            'roles' => $roles
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'page_name' => 'required|string|max:255',
            'page_route' => 'required|string|max:255|unique:page_permissions',
            'page_url' => 'required|string|max:255',
            'required_permissions' => 'nullable|array',
            'required_roles' => 'nullable|array',
            'is_active' => 'boolean'
        ]);

        PagePermission::create([
            'page_name' => $request->page_name,
            'page_route' => $request->page_route,
            'page_url' => $request->page_url,
            'required_permissions' => $request->required_permissions,
            'required_roles' => $request->required_roles,
            'is_active' => $request->is_active ?? true
        ]);

        return redirect()->route('admin.page-permissions.index')
            ->with('success', 'Page permission created successfully.');
    }

    public function edit(PagePermission $pagePermission)
    {
        $permissions = Permission::all();
        $roles = Role::all();

        return Inertia::render('admin/page-permissions/edit', [
            'pagePermission' => $pagePermission,
            'permissions' => $permissions,
            'roles' => $roles
        ]);
    }

    public function update(Request $request, PagePermission $pagePermission)
    {
        $request->validate([
            'page_name' => 'required|string|max:255',
            'page_route' => 'required|string|max:255|unique:page_permissions,page_route,' . $pagePermission->id,
            'page_url' => 'required|string|max:255',
            'required_permissions' => 'nullable|array',
            'required_roles' => 'nullable|array',
            'is_active' => 'boolean'
        ]);

        $pagePermission->update([
            'page_name' => $request->page_name,
            'page_route' => $request->page_route,
            'page_url' => $request->page_url,
            'required_permissions' => $request->required_permissions,
            'required_roles' => $request->required_roles,
            'is_active' => $request->is_active ?? true
        ]);

        return redirect()->route('admin.page-permissions.index')
            ->with('success', 'Page permission updated successfully.');
    }

    public function destroy(PagePermission $pagePermission)
    {
        $pagePermission->delete();

        return redirect()->route('admin.page-permissions.index')
            ->with('success', 'Page permission deleted successfully.');
    }

    public function toggle(PagePermission $pagePermission)
    {
        $pagePermission->update([
            'is_active' => !$pagePermission->is_active
        ]);

        return redirect()->route('admin.page-permissions.index')
            ->with('success', 'Page permission status updated successfully.');
    }
}
