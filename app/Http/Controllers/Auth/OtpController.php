<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\EmailTemplate;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Validation\ValidationException;
use Inertia\Inertia;
use Inertia\Response;

class OtpController extends Controller
{
    /**
     * Show the OTP request page.
     */
    public function create(): Response
    {
        return Inertia::render('auth/otp-request');
    }

    /**
     * Handle an incoming OTP request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request)
    {
        $request->validate([
            'email' => 'required|string|email|exists:users,email',
        ]);

        $user = User::where('email', $request->email)->first();
        if (!$user) {
            throw ValidationException::withMessages([
                'email' => 'User not found.',
            ]);
        }

        // Generate OTP
        $otp = str_pad((string) random_int(0, 999999), 6, '0', STR_PAD_LEFT);
        $user->otp = Hash::make($otp);
        $user->otp_expires_at = now()->addMinutes(10);
        $user->save();

        // Send OTP via email
        $template = EmailTemplate::getTemplate('otp-email');
        if ($template) {
            $data = [
                'user_name' => $user->name,
                'otp_code' => $otp,
                'expiry_time' => 10,
                'site_name' => Setting::get('site', 'name', 'Our Site'),
            ];
            $rendered = $template->render($data);
            Mail::raw($rendered['body'], function ($message) use ($user, $rendered) {
                $message->to($user->email)
                    ->subject($rendered['subject']);
            });
        }

        return back()->with('status', 'OTP sent to your email.');
    }

    /**
     * Show the OTP verification page.
     */
    public function verify(): Response
    {
        return Inertia::render('auth/otp-verify');
    }

    /**
     * Handle OTP verification.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function confirm(Request $request)
    {
        $request->validate([
            'email' => 'required|string|email|exists:users,email',
            'otp' => 'required|string|size:6',
        ]);

        $user = User::where('email', $request->email)->first();
        if (!$user || !$user->otp || !$user->otp_expires_at || $user->otp_expires_at->isPast()) {
            throw ValidationException::withMessages([
                'otp' => 'Invalid or expired OTP.',
            ]);
        }

        if (!Hash::check($request->otp, $user->otp)) {
            throw ValidationException::withMessages([
                'otp' => 'Invalid OTP.',
            ]);
        }

        $user->otp = null;
        $user->otp_expires_at = null;
        $user->save();

        return to_route('dashboard');
    }
} 