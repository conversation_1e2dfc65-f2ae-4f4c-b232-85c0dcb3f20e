<?php

namespace App\Http\Controllers;

use App\Models\Commission;
use App\Models\Module;
use App\Services\CommissionService;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class CommissionController extends Controller
{
    protected $commissionService;

    public function __construct(CommissionService $commissionService)
    {
        $this->commissionService = $commissionService;
    }

    /**
     * Display a listing of commissions.
     */
    public function index(): Response
    {
        $user = auth()->user();
        $commissions = $this->commissionService->getCommissions($user);

        return Inertia::render('commissions/index', [
            'commissions' => $commissions,
        ]);
    }

    /**
     * Show the form for creating a new commission.
     */
    public function create(): Response
    {
        $modules = Module::where('is_active', true)->get();
        return Inertia::render('commissions/create', [
            'modules' => $modules,
        ]);
    }

    /**
     * Store a newly created commission in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'module_id' => 'required|exists:modules,id',
            'percentage' => 'required|numeric|min:0|max:100',
        ]);

        $user = auth()->user();
        $module = Module::findOrFail($request->module_id);

        $this->commissionService->setCommission($user, $module, $request->percentage);

        return redirect()->route('commissions.index')
            ->with('success', 'Commission rate set successfully.');
    }

    /**
     * Show the form for editing the specified commission.
     */
    public function edit(Commission $commission): Response
    {
        $modules = Module::where('is_active', true)->get();
        return Inertia::render('commissions/edit', [
            'commission' => $commission->load('module'),
            'modules' => $modules,
        ]);
    }

    /**
     * Update the specified commission in storage.
     */
    public function update(Request $request, Commission $commission)
    {
        $request->validate([
            'module_id' => 'required|exists:modules,id',
            'percentage' => 'required|numeric|min:0|max:100',
        ]);

        $user = auth()->user();
        $module = Module::findOrFail($request->module_id);

        $this->commissionService->setCommission($user, $module, $request->percentage);

        return redirect()->route('commissions.index')
            ->with('success', 'Commission rate updated successfully.');
    }

    /**
     * Remove the specified commission from storage.
     */
    public function destroy(Commission $commission)
    {
        $user = auth()->user();
        $this->commissionService->removeCommission($user, $commission->module);

        return redirect()->route('commissions.index')
            ->with('success', 'Commission rate removed successfully.');
    }
} 