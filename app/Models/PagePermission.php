<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PagePermission extends Model
{
    protected $fillable = [
        'page_name',
        'page_route',
        'page_url',
        'required_permissions',
        'required_roles',
        'is_active'
    ];

    protected $casts = [
        'required_permissions' => 'array',
        'required_roles' => 'array',
        'is_active' => 'boolean'
    ];

    public function hasRequiredPermissions($userPermissions)
    {
        if (empty($this->required_permissions)) {
            return true;
        }

        foreach ($this->required_permissions as $permission) {
            if (in_array($permission, $userPermissions)) {
                return true;
            }
        }

        return false;
    }

    public function hasRequiredRoles($userRole)
    {
        if (empty($this->required_roles)) {
            return true;
        }

        return in_array($userRole, $this->required_roles);
    }
}
