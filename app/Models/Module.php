<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Module extends Model
{
    use SoftDeletes, HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'version',
        'description',
        'author',
        'author_email',
        'author_url',
        'dependencies',
        'is_active',
        'settings'
    ];

    protected $casts = [
        'dependencies' => 'array',
        'settings' => 'array',
        'is_active' => 'boolean'
    ];

    public function commissions()
    {
        return $this->hasMany(Commission::class);
    }

    public function transactions()
    {
        return $this->hasMany(Transaction::class);
    }

    public function install()
    {
        // Run module migrations
        $migrationPath = storage_path("app/modules/{$this->slug}/database/migrations");
        if (file_exists($migrationPath)) {
            \Artisan::call('migrate', [
                '--path' => "storage/app/modules/{$this->slug}/database/migrations",
                '--force' => true
            ]);
        }

        $this->is_active = true;
        $this->save();
    }

    public function uninstall()
    {
        // Rollback module migrations
        $migrationPath = storage_path("app/modules/{$this->slug}/database/migrations");
        if (file_exists($migrationPath)) {
            \Artisan::call('migrate:rollback', [
                '--path' => "storage/app/modules/{$this->slug}/database/migrations",
                '--force' => true
            ]);
        }

        // Remove module commissions
        $this->commissions()->delete();

        $this->is_active = false;
        $this->save();
    }

    public function getModulePath(): string
    {
        return base_path('modules/' . $this->slug);
    }

    public function getMigrationPath(): string
    {
        return $this->getModulePath() . '/database/migrations';
    }

    public function getConfigPath(): string
    {
        return $this->getModulePath() . '/config';
    }

    public function getRoutesPath(): string
    {
        return $this->getModulePath() . '/routes';
    }

    public function getViewsPath(): string
    {
        return $this->getModulePath() . '/resources/views';
    }

    public function getAssetsPath(): string
    {
        return $this->getModulePath() . '/resources/assets';
    }
} 