<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class OtpTemplate extends Model
{
    protected $fillable = [
        'type',
        'name',
        'subject',
        'body',
        'variables',
        'is_active',
    ];

    protected $casts = [
        'variables' => 'array',
        'is_active' => 'boolean',
    ];

    public function getAvailableVariables()
    {
        return [
            '{otp}' => 'The OTP code',
            '{name}' => 'User\'s name',
            '{email}' => 'User\'s email',
            '{phone}' => 'User\'s phone number',
            '{expiry_time}' => 'OTP expiry time',
        ];
    }
} 