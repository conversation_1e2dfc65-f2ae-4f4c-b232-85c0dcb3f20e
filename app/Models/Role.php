<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Role extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'permissions'
    ];

    protected $casts = [
        'permissions' => 'array'
    ];

    public function users()
    {
        return $this->hasMany(User::class);
    }

    public function permissions()
    {
        return $this->belongsToMany(Permission::class, 'role_permissions');
    }

    public function hasPermission($permission)
    {
        // Check both old permissions array and new permission relationships
        if (in_array($permission, $this->permissions ?? [])) {
            return true;
        }

        return $this->permissions()->where('slug', $permission)->exists();
    }

    public function getPermissionSlugs()
    {
        return $this->permissions()->pluck('slug')->toArray();
    }
}