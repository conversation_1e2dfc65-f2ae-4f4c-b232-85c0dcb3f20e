<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Setting extends Model
{
    protected $fillable = [
        'group',
        'key',
        'value',
        'type',
        'is_public'
    ];

    protected $casts = [
        'is_public' => 'boolean'
    ];

    public static function get($group, $key, $default = null)
    {
        $setting = static::where('group', $group)
            ->where('key', $key)
            ->first();

        if (!$setting) {
            return $default;
        }

        return static::castValue($setting->value, $setting->type);
    }

    public static function set($group, $key, $value, $type = 'string', $isPublic = false)
    {
        $setting = static::firstOrNew([
            'group' => $group,
            'key' => $key
        ]);

        $setting->value = $value;
        $setting->type = $type;
        $setting->is_public = $isPublic;
        $setting->save();

        return $setting;
    }

    protected static function castValue($value, $type)
    {
        switch ($type) {
            case 'integer':
                return (int) $value;
            case 'float':
                return (float) $value;
            case 'boolean':
                return (bool) $value;
            case 'json':
                return json_decode($value, true);
            default:
                return $value;
        }
    }
} 