<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class OtpSetting extends Model
{
    protected $fillable = [
        'email_otp_enabled',
        'phone_otp_enabled',
        'whatsapp_otp_enabled',
        'telegram_otp_enabled',
        'email_verification_required',
        'phone_verification_required',
        'whatsapp_verification_required',
        'telegram_verification_required',
        'otp_expiry_minutes',
        'max_otp_attempts',
    ];

    protected $casts = [
        'email_otp_enabled' => 'boolean',
        'phone_otp_enabled' => 'boolean',
        'whatsapp_otp_enabled' => 'boolean',
        'telegram_otp_enabled' => 'boolean',
        'email_verification_required' => 'boolean',
        'phone_verification_required' => 'boolean',
        'whatsapp_verification_required' => 'boolean',
        'telegram_verification_required' => 'boolean',
    ];
} 