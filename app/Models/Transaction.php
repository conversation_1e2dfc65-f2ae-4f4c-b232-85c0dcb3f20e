<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Transaction extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'user_id',
        'module_id',
        'type',
        'amount',
        'balance_before',
        'balance_after',
        'reference_id',
        'description',
        'metadata',
        'status'
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'balance_before' => 'decimal:2',
        'balance_after' => 'decimal:2',
        'metadata' => 'array'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function module()
    {
        return $this->belongsTo(Module::class);
    }

    public static function createTransaction($user, $amount, $type, $module = null, $referenceId = null, $description = null, $metadata = [])
    {
        $transaction = new static();
        $transaction->user_id = $user->id;
        $transaction->module_id = $module ? $module->id : null;
        $transaction->type = $type;
        $transaction->amount = $amount;
        $transaction->balance_before = $user->balance;
        $transaction->reference_id = $referenceId;
        $transaction->description = $description;
        $transaction->metadata = $metadata;
        $transaction->status = 'pending';
        $transaction->save();

        // Update user balance
        $user->updateBalance($amount, $type);
        $transaction->balance_after = $user->balance;
        $transaction->status = 'completed';
        $transaction->save();

        return $transaction;
    }

    public function processCommissions()
    {
        if (!$this->module_id || $this->type !== 'credit') {
            return;
        }

        $user = $this->user;
        while ($user->parent) {
            $user = $user->parent;
            $commission = $user->commissions()
                ->where('module_id', $this->module_id)
                ->where('is_active', true)
                ->first();

            if ($commission) {
                $commissionAmount = $commission->calculateCommission($this->amount);
                static::createTransaction(
                    $user,
                    $commissionAmount,
                    'credit',
                    $this->module,
                    "COMM_{$this->id}",
                    "Commission from transaction #{$this->id}",
                    ['source_transaction_id' => $this->id]
                );
            }
        }
    }
} 