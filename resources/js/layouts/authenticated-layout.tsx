import { ReactNode } from 'react';
import AppLayout from './app/app-sidebar-layout';

interface Props {
    user: {
        name: string;
        email: string;
    };
    header?: ReactNode;
    children: ReactNode;
}

export default function AuthenticatedLayout({ user, header, children }: Props) {
    return (
        <AppLayout>
            <div className="min-h-screen">
                {header && (
                    <header className="border-sidebar-border/80 border-b">
                        <div className="mx-auto flex h-16 items-center px-4 md:max-w-7xl">
                            {header}
                        </div>
                    </header>
                )}

                <main>{children}</main>
            </div>
        </AppLayout>
    );
} 