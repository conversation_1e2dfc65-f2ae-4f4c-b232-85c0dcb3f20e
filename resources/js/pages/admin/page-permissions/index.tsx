import { <PERSON>, <PERSON>, router } from '@inertiajs/react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Switch } from '@/components/ui/switch';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Plus, Edit, Trash2, Lock, Shield } from 'lucide-react';
import AppLayout from '@/layouts/app-layout';

interface Permission {
    id: number;
    name: string;
    slug: string;
}

interface Role {
    id: number;
    name: string;
    slug: string;
}

interface PagePermission {
    id: number;
    page_name: string;
    page_route: string;
    page_url: string;
    required_permissions: string[];
    required_roles: string[];
    is_active: boolean;
}

interface Props {
    pagePermissions: PagePermission[];
    permissions: Permission[];
    roles: Role[];
}

export default function PagePermissionIndex({ pagePermissions, permissions, roles }: Props) {
    const handleToggle = (pagePermission: PagePermission) => {
        router.post(route('admin.page-permissions.toggle', pagePermission.id));
    };

    const handleDelete = (pagePermission: PagePermission) => {
        router.delete(route('admin.page-permissions.destroy', pagePermission.id));
    };

    const getPermissionNames = (permissionSlugs: string[]) => {
        return permissionSlugs.map(slug => 
            permissions.find(p => p.slug === slug)?.name || slug
        );
    };

    const getRoleNames = (roleSlugs: string[]) => {
        return roleSlugs.map(slug => 
            roles.find(r => r.slug === slug)?.name || slug
        );
    };

    return (
        <AppLayout>
            <Head title="Page Permissions" />
            
            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Page Permissions</h1>
                        <p className="text-muted-foreground">
                            Manage which pages are accessible to which roles and permissions
                        </p>
                    </div>
                    <Link href={route('admin.page-permissions.create')}>
                        <Button>
                            <Plus className="mr-2 h-4 w-4" />
                            Add Page Permission
                        </Button>
                    </Link>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Lock className="h-5 w-5" />
                            Page Access Control
                        </CardTitle>
                        <CardDescription>
                            Configure which pages require specific roles or permissions
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Page</TableHead>
                                    <TableHead>Route</TableHead>
                                    <TableHead>Required Roles</TableHead>
                                    <TableHead>Required Permissions</TableHead>
                                    <TableHead>Status</TableHead>
                                    <TableHead className="text-right">Actions</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {pagePermissions.map((pagePermission) => (
                                    <TableRow key={pagePermission.id}>
                                        <TableCell>
                                            <div>
                                                <div className="font-medium">{pagePermission.page_name}</div>
                                                <div className="text-sm text-muted-foreground">
                                                    {pagePermission.page_url}
                                                </div>
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            <code className="text-xs bg-muted px-2 py-1 rounded">
                                                {pagePermission.page_route}
                                            </code>
                                        </TableCell>
                                        <TableCell>
                                            <div className="flex flex-wrap gap-1">
                                                {pagePermission.required_roles?.length > 0 ? (
                                                    getRoleNames(pagePermission.required_roles).map((roleName, index) => (
                                                        <Badge key={index} variant="default" className="text-xs">
                                                            <Shield className="mr-1 h-3 w-3" />
                                                            {roleName}
                                                        </Badge>
                                                    ))
                                                ) : (
                                                    <span className="text-sm text-muted-foreground">None</span>
                                                )}
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            <div className="flex flex-wrap gap-1">
                                                {pagePermission.required_permissions?.length > 0 ? (
                                                    getPermissionNames(pagePermission.required_permissions).slice(0, 2).map((permissionName, index) => (
                                                        <Badge key={index} variant="secondary" className="text-xs">
                                                            {permissionName}
                                                        </Badge>
                                                    ))
                                                ) : (
                                                    <span className="text-sm text-muted-foreground">None</span>
                                                )}
                                                {pagePermission.required_permissions?.length > 2 && (
                                                    <Badge variant="outline" className="text-xs">
                                                        +{pagePermission.required_permissions.length - 2} more
                                                    </Badge>
                                                )}
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            <Switch
                                                checked={pagePermission.is_active}
                                                onCheckedChange={() => handleToggle(pagePermission)}
                                            />
                                        </TableCell>
                                        <TableCell className="text-right">
                                            <div className="flex items-center justify-end gap-2">
                                                <Link href={route('admin.page-permissions.edit', pagePermission.id)}>
                                                    <Button variant="outline" size="sm">
                                                        <Edit className="h-4 w-4" />
                                                    </Button>
                                                </Link>
                                                <AlertDialog>
                                                    <AlertDialogTrigger asChild>
                                                        <Button variant="outline" size="sm">
                                                            <Trash2 className="h-4 w-4" />
                                                        </Button>
                                                    </AlertDialogTrigger>
                                                    <AlertDialogContent>
                                                        <AlertDialogHeader>
                                                            <AlertDialogTitle>Delete Page Permission</AlertDialogTitle>
                                                            <AlertDialogDescription>
                                                                Are you sure you want to delete the page permission for "{pagePermission.page_name}"? 
                                                                This action cannot be undone.
                                                            </AlertDialogDescription>
                                                        </AlertDialogHeader>
                                                        <AlertDialogFooter>
                                                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                            <AlertDialogAction 
                                                                onClick={() => handleDelete(pagePermission)}
                                                                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                                            >
                                                                Delete
                                                            </AlertDialogAction>
                                                        </AlertDialogFooter>
                                                    </AlertDialogContent>
                                                </AlertDialog>
                                            </div>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
