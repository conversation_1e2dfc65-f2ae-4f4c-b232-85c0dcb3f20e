import { Head, useForm, router } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { Package, Upload, Power, Trash2, RefreshCw } from 'lucide-react';
import { useState } from 'react';

interface BreadcrumbItem {
    title: string;
    href: string;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Modules',
        href: '/admin/modules',
    },
];

interface Module {
    id: number;
    name: string;
    slug: string;
    version: string;
    description: string | null;
    author: string | null;
    author_url: string | null;
    is_active: boolean;
    settings: Record<string, unknown> | null;
    created_at: string;
    updated_at: string;
}

interface Props {
    modules: Module[];
}

export default function ModuleManagement({ modules }: Props) {
    const [isUploading, setIsUploading] = useState(false);
    const [isRefreshing, setIsRefreshing] = useState(false);
    const form = useForm({
        module: null as File | null,
    });

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            form.setData('module', e.target.files[0]);
        }
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        setIsUploading(true);

        form.post(route('admin.modules.store'), {
            onSuccess: () => {
                toast.success('Module uploaded successfully');
                setIsUploading(false);
                form.reset();
            },
            onError: () => {
                toast.error('Failed to upload module');
                setIsUploading(false);
            },
        });
    };

    const handleActivate = (moduleId: number) => {
        router.post(route('admin.modules.activate', moduleId), {}, {
            onSuccess: () => {
                toast.success('Module activated successfully');
            },
            onError: () => {
                toast.error('Failed to activate module');
            },
        });
    };

    const handleDeactivate = (moduleId: number) => {
        router.post(route('admin.modules.deactivate', moduleId), {}, {
            onSuccess: () => {
                toast.success('Module deactivated successfully');
            },
            onError: () => {
                toast.error('Failed to deactivate module');
            },
        });
    };

    const handleDelete = (moduleId: number) => {
        if (confirm('Are you sure you want to delete this module? This action cannot be undone.')) {
            router.delete(route('admin.modules.destroy', moduleId), {
                onSuccess: () => {
                    toast.success('Module deleted successfully');
                },
                onError: () => {
                    toast.error('Failed to delete module');
                },
            });
        }
    };

    const handleRefresh = () => {
        setIsRefreshing(true);
        router.post(route('admin.modules.refresh'), {}, {
            onSuccess: () => {
                toast.success('Modules refreshed successfully');
                setIsRefreshing(false);
            },
            onError: () => {
                toast.error('Failed to refresh modules');
                setIsRefreshing(false);
            },
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Module Management" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    <div className="grid gap-6">
                        {/* Upload Module Card */}
                        <Card>
                            <CardHeader>
                                <div className="flex items-center justify-between">
                                    <CardTitle>Upload New Module</CardTitle>
                                    <Button
                                        variant="outline"
                                        onClick={handleRefresh}
                                        disabled={isRefreshing}
                                    >
                                        <RefreshCw className={`mr-2 h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                                        {isRefreshing ? 'Refreshing...' : 'Refresh Modules'}
                                    </Button>
                                </div>
                            </CardHeader>
                            <CardContent>
                                <form onSubmit={handleSubmit} className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="module">Module Package (ZIP)</Label>
                                        <Input
                                            id="module"
                                            type="file"
                                            accept=".zip"
                                            onChange={handleFileChange}
                                            disabled={isUploading}
                                        />
                                    </div>
                                    <Button type="submit" disabled={isUploading || !form.data.module}>
                                        <Upload className="mr-2 h-4 w-4" />
                                        {isUploading ? 'Uploading...' : 'Upload Module'}
                                    </Button>
                                </form>
                            </CardContent>
                        </Card>

                        {/* Installed Modules Card */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Installed Modules</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    {modules.length === 0 ? (
                                        <p className="text-center text-muted-foreground py-4">
                                            No modules installed yet.
                                        </p>
                                    ) : (
                                        modules.map((module) => (
                                            <Card key={module.id} className="p-4">
                                                <div className="flex items-center justify-between">
                                                    <div className="flex items-center space-x-4">
                                                        <Package className="h-8 w-8 text-muted-foreground" />
                                                        <div>
                                                            <h3 className="font-medium">{module.name}</h3>
                                                            <p className="text-sm text-muted-foreground">
                                                                Version {module.version}
                                                            </p>
                                                            {module.description && (
                                                                <p className="text-sm text-muted-foreground mt-1">
                                                                    {module.description}
                                                                </p>
                                                            )}
                                                        </div>
                                                    </div>
                                                    <div className="flex items-center space-x-2">
                                                        {module.is_active ? (
                                                            <Button
                                                                variant="outline"
                                                                size="sm"
                                                                onClick={() => handleDeactivate(module.id)}
                                                            >
                                                                <Power className="mr-2 h-4 w-4" />
                                                                Deactivate
                                                            </Button>
                                                        ) : (
                                                            <Button
                                                                variant="outline"
                                                                size="sm"
                                                                onClick={() => handleActivate(module.id)}
                                                            >
                                                                <Power className="mr-2 h-4 w-4" />
                                                                Activate
                                                            </Button>
                                                        )}
                                                        <Button
                                                            variant="destructive"
                                                            size="sm"
                                                            onClick={() => handleDelete(module.id)}
                                                        >
                                                            <Trash2 className="mr-2 h-4 w-4" />
                                                            Delete
                                                        </Button>
                                                    </div>
                                                </div>
                                            </Card>
                                        ))
                                    )}
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
} 