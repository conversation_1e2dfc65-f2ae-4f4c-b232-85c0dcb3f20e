import { Head, useForm } from '@inertiajs/react';
import { AlertCircle, LoaderCircle } from 'lucide-react';
import { FormEventHandler } from 'react';

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { But<PERSON> } from '@/components/ui/button';
import AuthLayout from '@/layouts/auth-layout';

interface Props {
    adminCreated?: boolean;
    credentials?: {
        email: string;
        password: string;
    };
}

export default function AdminSetup({ adminCreated, credentials }: Props) {
    const { post, processing } = useForm();

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('admin.setup'));
    };

    return (
        <AuthLayout title="Admin Setup" description="Create the initial admin account">
            <Head title="Admin Setup" />

            {adminCreated && credentials ? (
                <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Admin Account Created</AlertTitle>
                    <AlertDescription className="mt-2">
                        <p className="mb-2">Please save these credentials:</p>
                        <div className="rounded-md bg-muted p-3">
                            <p><strong>Email:</strong> {credentials.email}</p>
                            <p><strong>Password:</strong> {credentials.password}</p>
                        </div>
                        <p className="mt-2 text-sm text-muted-foreground">
                            You can use these credentials to log in to the admin dashboard.
                            Please change the password after your first login.
                        </p>
                    </AlertDescription>
                </Alert>
            ) : (
                <form className="flex flex-col gap-6" onSubmit={submit}>
                    <div className="text-center">
                        <p className="text-muted-foreground">
                            No admin account has been created yet. Click the button below to create the initial admin account.
                        </p>
                    </div>

                    <Button type="submit" disabled={processing}>
                        {processing && <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />}
                        Create Admin Account
                    </Button>
                </form>
            )}
        </AuthLayout>
    );
} 