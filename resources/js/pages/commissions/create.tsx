import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, useForm } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowLeft } from 'lucide-react';
import { Link } from '@inertiajs/react';
import InputError from '@/components/input-error';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Commissions',
        href: '/commissions',
    },
    {
        title: 'Set Commission Rate',
        href: '/commissions/create',
    },
];

interface Module {
    id: number;
    name: string;
    slug: string;
}

interface Props {
    modules: Module[];
}

export default function CreateCommission({ modules }: Props) {
    const { data, setData, post, processing, errors } = useForm({
        module_id: '',
        percentage: '',
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('commissions.store'));
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Set Commission Rate" />
            <div className="flex h-full flex-1 flex-col gap-4 p-4">
                <div className="flex items-center gap-4">
                    <Button asChild variant="outline" size="icon">
                        <Link href={route('commissions.index')}>
                            <ArrowLeft className="h-4 w-4" />
                        </Link>
                    </Button>
                    <h1 className="text-2xl font-semibold">Set Commission Rate</h1>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle>Set Commission Rate</CardTitle>
                        <CardDescription>Set commission rate for a module</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="module_id">Module</Label>
                                <Select
                                    value={data.module_id}
                                    onValueChange={(value) => setData('module_id', value)}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select a module" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {modules.map((module) => (
                                            <SelectItem key={module.id} value={module.id.toString()}>
                                                {module.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                <InputError message={errors.module_id} />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="percentage">Commission Rate (%)</Label>
                                <Input
                                    id="percentage"
                                    type="number"
                                    step="0.01"
                                    min="0"
                                    max="100"
                                    value={data.percentage}
                                    onChange={(e) => setData('percentage', e.target.value)}
                                    placeholder="Enter commission rate"
                                />
                                <InputError message={errors.percentage} />
                            </div>

                            <div className="flex justify-end">
                                <Button type="submit" disabled={processing}>
                                    {processing ? 'Setting...' : 'Set Commission Rate'}
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
} 