import { Head } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useState } from 'react';
import { useForm, router } from '@inertiajs/react';
import { toast } from 'sonner';

interface BreadcrumbItem {
    title: string;
    href: string;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Settings',
        href: '/settings',
    },
    {
        title: 'OTP Settings',
        href: '/settings/otp',
    },
];

interface OtpSettings {
    email_otp_enabled: boolean;
    phone_otp_enabled: boolean;
    whatsapp_otp_enabled: boolean;
    telegram_otp_enabled: boolean;
    email_verification_required: boolean;
    phone_verification_required: boolean;
    whatsapp_verification_required: boolean;
    telegram_verification_required: boolean;
    otp_expiry_minutes: number;
    max_otp_attempts: number;
    [key: string]: string | number | boolean;
}

interface SmtpSettings {
    mailer: string;
    host: string;
    port: number;
    username: string;
    password: string;
    encryption: string;
    from_address: string;
    from_name: string;
    is_active: boolean;
    [key: string]: string | number | boolean;
}

interface OtpTemplate {
    id: number;
    type: string;
    name: string;
    subject: string | null;
    body: string;
    is_active: boolean;
}

interface Props {
    otpSettings: OtpSettings;
    smtpSettings: SmtpSettings;
    otpTemplates: OtpTemplate[];
}

export default function OtpSettings({ otpSettings, smtpSettings, otpTemplates }: Props) {
    const [activeTab, setActiveTab] = useState('general');

    const otpForm = useForm<OtpSettings>({
        ...otpSettings,
    });

    const smtpForm = useForm<SmtpSettings>({
        ...smtpSettings,
    });

    const handleOtpSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        otpForm.put(route('admin.settings.otp.update'), {
            onSuccess: () => {
                toast.success('OTP settings updated successfully');
            },
            onError: () => {
                toast.error('Failed to update OTP settings');
            },
        });
    };

    const handleSmtpSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        smtpForm.put(route('admin.settings.smtp.update'), {
            onSuccess: () => {
                toast.success('SMTP settings updated successfully');
            },
            onError: () => {
                toast.error('Failed to update SMTP settings');
            },
        });
    };

    const testSmtpConnection = () => {
        smtpForm.post(route('admin.settings.smtp.test'), {
            onSuccess: () => {
                toast.success('SMTP connection successful');
            },
            onError: (errors: { message?: string }) => {
                toast.error('SMTP connection failed: ' + (errors.message || 'Unknown error'));
            },
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="OTP Settings" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    <Tabs value={activeTab} onValueChange={setActiveTab}>
                        <TabsList>
                            <TabsTrigger value="general">General Settings</TabsTrigger>
                            <TabsTrigger value="smtp">SMTP Settings</TabsTrigger>
                            <TabsTrigger value="templates">Message Templates</TabsTrigger>
                        </TabsList>

                        <TabsContent value="general">
                            <Card>
                                <CardHeader>
                                    <CardTitle>OTP Settings</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <form onSubmit={handleOtpSubmit} className="space-y-6">
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            <div className="space-y-4">
                                                <h3 className="text-lg font-medium">Enable OTP Methods</h3>
                                                <div className="space-y-2">
                                                    <div className="flex items-center justify-between">
                                                        <Label htmlFor="email_otp_enabled">Email OTP</Label>
                                                        <Switch
                                                            id="email_otp_enabled"
                                                            checked={otpForm.data.email_otp_enabled}
                                                            onCheckedChange={(checked: boolean) =>
                                                                otpForm.setData('email_otp_enabled', checked)
                                                            }
                                                        />
                                                    </div>
                                                    <div className="flex items-center justify-between">
                                                        <Label htmlFor="phone_otp_enabled">Phone OTP</Label>
                                                        <Switch
                                                            id="phone_otp_enabled"
                                                            checked={otpForm.data.phone_otp_enabled}
                                                            onCheckedChange={(checked: boolean) =>
                                                                otpForm.setData('phone_otp_enabled', checked)
                                                            }
                                                        />
                                                    </div>
                                                    <div className="flex items-center justify-between">
                                                        <Label htmlFor="whatsapp_otp_enabled">WhatsApp OTP</Label>
                                                        <Switch
                                                            id="whatsapp_otp_enabled"
                                                            checked={otpForm.data.whatsapp_otp_enabled}
                                                            onCheckedChange={(checked: boolean) =>
                                                                otpForm.setData('whatsapp_otp_enabled', checked)
                                                            }
                                                        />
                                                    </div>
                                                    <div className="flex items-center justify-between">
                                                        <Label htmlFor="telegram_otp_enabled">Telegram OTP</Label>
                                                        <Switch
                                                            id="telegram_otp_enabled"
                                                            checked={otpForm.data.telegram_otp_enabled}
                                                            onCheckedChange={(checked: boolean) =>
                                                                otpForm.setData('telegram_otp_enabled', checked)
                                                            }
                                                        />
                                                    </div>
                                                </div>
                                            </div>

                                            <div className="space-y-4">
                                                <h3 className="text-lg font-medium">Required Verifications</h3>
                                                <div className="space-y-2">
                                                    <div className="flex items-center justify-between">
                                                        <Label htmlFor="email_verification_required">Email Verification</Label>
                                                        <Switch
                                                            id="email_verification_required"
                                                            checked={otpForm.data.email_verification_required}
                                                            onCheckedChange={(checked: boolean) =>
                                                                otpForm.setData('email_verification_required', checked)
                                                            }
                                                        />
                                                    </div>
                                                    <div className="flex items-center justify-between">
                                                        <Label htmlFor="phone_verification_required">Phone Verification</Label>
                                                        <Switch
                                                            id="phone_verification_required"
                                                            checked={otpForm.data.phone_verification_required}
                                                            onCheckedChange={(checked: boolean) =>
                                                                otpForm.setData('phone_verification_required', checked)
                                                            }
                                                        />
                                                    </div>
                                                    <div className="flex items-center justify-between">
                                                        <Label htmlFor="whatsapp_verification_required">WhatsApp Verification</Label>
                                                        <Switch
                                                            id="whatsapp_verification_required"
                                                            checked={otpForm.data.whatsapp_verification_required}
                                                            onCheckedChange={(checked: boolean) =>
                                                                otpForm.setData('whatsapp_verification_required', checked)
                                                            }
                                                        />
                                                    </div>
                                                    <div className="flex items-center justify-between">
                                                        <Label htmlFor="telegram_verification_required">Telegram Verification</Label>
                                                        <Switch
                                                            id="telegram_verification_required"
                                                            checked={otpForm.data.telegram_verification_required}
                                                            onCheckedChange={(checked: boolean) =>
                                                                otpForm.setData('telegram_verification_required', checked)
                                                            }
                                                        />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            <div className="space-y-2">
                                                <Label htmlFor="otp_expiry_minutes">OTP Expiry (minutes)</Label>
                                                <Input
                                                    id="otp_expiry_minutes"
                                                    type="number"
                                                    min="1"
                                                    max="60"
                                                    value={otpForm.data.otp_expiry_minutes}
                                                    onChange={(e) =>
                                                        otpForm.setData('otp_expiry_minutes', parseInt(e.target.value))
                                                    }
                                                />
                                            </div>
                                            <div className="space-y-2">
                                                <Label htmlFor="max_otp_attempts">Max OTP Attempts</Label>
                                                <Input
                                                    id="max_otp_attempts"
                                                    type="number"
                                                    min="1"
                                                    max="10"
                                                    value={otpForm.data.max_otp_attempts}
                                                    onChange={(e) =>
                                                        otpForm.setData('max_otp_attempts', parseInt(e.target.value))
                                                    }
                                                />
                                            </div>
                                        </div>

                                        <Button type="submit" disabled={otpForm.processing}>
                                            Save OTP Settings
                                        </Button>
                                    </form>
                                </CardContent>
                            </Card>
                        </TabsContent>

                        <TabsContent value="smtp">
                            <Card>
                                <CardHeader>
                                    <CardTitle>SMTP Settings</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <form onSubmit={handleSmtpSubmit} className="space-y-6">
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            <div className="space-y-4">
                                                <div className="space-y-2">
                                                    <Label htmlFor="mailer">Mailer</Label>
                                                    <Input
                                                        id="mailer"
                                                        value={smtpForm.data.mailer}
                                                        onChange={(e) => smtpForm.setData('mailer', e.target.value)}
                                                    />
                                                </div>
                                                <div className="space-y-2">
                                                    <Label htmlFor="host">Host</Label>
                                                    <Input
                                                        id="host"
                                                        value={smtpForm.data.host}
                                                        onChange={(e) => smtpForm.setData('host', e.target.value)}
                                                    />
                                                </div>
                                                <div className="space-y-2">
                                                    <Label htmlFor="port">Port</Label>
                                                    <Input
                                                        id="port"
                                                        type="number"
                                                        value={smtpForm.data.port}
                                                        onChange={(e) =>
                                                            smtpForm.setData('port', parseInt(e.target.value))
                                                        }
                                                    />
                                                </div>
                                                <div className="space-y-2">
                                                    <Label htmlFor="username">Username</Label>
                                                    <Input
                                                        id="username"
                                                        value={smtpForm.data.username}
                                                        onChange={(e) => smtpForm.setData('username', e.target.value)}
                                                    />
                                                </div>
                                            </div>

                                            <div className="space-y-4">
                                                <div className="space-y-2">
                                                    <Label htmlFor="password">Password</Label>
                                                    <Input
                                                        id="password"
                                                        type="password"
                                                        value={smtpForm.data.password}
                                                        onChange={(e) => smtpForm.setData('password', e.target.value)}
                                                    />
                                                </div>
                                                <div className="space-y-2">
                                                    <Label htmlFor="encryption">Encryption</Label>
                                                    <select
                                                        id="encryption"
                                                        className="w-full rounded-md border border-input bg-background px-3 py-2"
                                                        value={smtpForm.data.encryption}
                                                        onChange={(e) => smtpForm.setData('encryption', e.target.value)}
                                                    >
                                                        <option value="tls">TLS</option>
                                                        <option value="ssl">SSL</option>
                                                    </select>
                                                </div>
                                                <div className="space-y-2">
                                                    <Label htmlFor="from_address">From Address</Label>
                                                    <Input
                                                        id="from_address"
                                                        type="email"
                                                        value={smtpForm.data.from_address}
                                                        onChange={(e) => smtpForm.setData('from_address', e.target.value)}
                                                    />
                                                </div>
                                                <div className="space-y-2">
                                                    <Label htmlFor="from_name">From Name</Label>
                                                    <Input
                                                        id="from_name"
                                                        value={smtpForm.data.from_name}
                                                        onChange={(e) => smtpForm.setData('from_name', e.target.value)}
                                                    />
                                                </div>
                                            </div>
                                        </div>

                                        <div className="flex items-center space-x-2">
                                            <Switch
                                                id="smtp_is_active"
                                                checked={smtpForm.data.is_active}
                                                onCheckedChange={(checked: boolean) => smtpForm.setData('is_active', checked)}
                                            />
                                            <Label htmlFor="smtp_is_active">Enable SMTP</Label>
                                        </div>

                                        <div className="flex space-x-4">
                                            <Button type="submit" disabled={smtpForm.processing}>
                                                Save SMTP Settings
                                            </Button>
                                            <Button
                                                type="button"
                                                variant="outline"
                                                onClick={testSmtpConnection}
                                                disabled={smtpForm.processing}
                                            >
                                                Test Connection
                                            </Button>
                                        </div>
                                    </form>
                                </CardContent>
                            </Card>
                        </TabsContent>

                        <TabsContent value="templates">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Message Templates</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    {otpTemplates.length === 0 ? (
                                        <div className="text-center py-8">
                                            <p className="text-gray-500 mb-4">No message templates found.</p>
                                            <Button
                                                onClick={() => {
                                                    router.post(route('admin.settings.otp.create-default-templates'), {}, {
                                                        onSuccess: () => {
                                                            toast.success('Default templates created successfully');
                                                            window.location.reload();
                                                        },
                                                        onError: (errors) => {
                                                            console.error('Error creating templates:', errors);
                                                            toast.error('Failed to create default templates');
                                                        },
                                                    });
                                                }}
                                            >
                                                Create Default Templates
                                            </Button>
                                        </div>
                                    ) : (
                                        <div className="space-y-4">
                                            {otpTemplates.map((template) => (
                                                <Card key={template.id}>
                                                    <CardHeader>
                                                        <div className="flex items-center justify-between">
                                                            <div>
                                                                <CardTitle>{template.name}</CardTitle>
                                                                <p className="text-sm text-gray-500">
                                                                    Type: {template.type}
                                                                </p>
                                                            </div>
                                                            <Switch
                                                                checked={template.is_active}
                                                                onCheckedChange={() => {
                                                                    // Handle template activation/deactivation
                                                                }}
                                                            />
                                                        </div>
                                                    </CardHeader>
                                                    <CardContent>
                                                        <div className="space-y-2">
                                                            {template.subject && (
                                                                <div>
                                                                    <Label>Subject</Label>
                                                                    <p className="text-sm">{template.subject}</p>
                                                                </div>
                                                            )}
                                                            <div>
                                                                <Label>Body</Label>
                                                                <p className="text-sm whitespace-pre-wrap">{template.body}</p>
                                                            </div>
                                                        </div>
                                                    </CardContent>
                                                </Card>
                                            ))}
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        </TabsContent>
                    </Tabs>
                </div>
            </div>
        </AppLayout>
    );
} 