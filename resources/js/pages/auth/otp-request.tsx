import { Head, useForm } from '@inertiajs/react';
import { LoaderCircle } from 'lucide-react';
import { FormEventHandler } from 'react';

import InputError from '@/components/input-error';
import TextLink from '@/components/text-link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AuthLayout from '@/layouts/auth-layout';

type OtpRequestForm = {
    email: string;
};

export default function OtpRequest() {
    const { data, setData, post, processing, errors, reset } = useForm<Required<OtpRequestForm>>({
        email: '',
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('otp.store'), {
            onFinish: () => reset('email'),
        });
    };

    return (
        <AuthLayout title="Request OTP" description="Enter your email to receive an OTP">
            <Head title="Request OTP" />
            <form className="flex flex-col gap-6" onSubmit={submit}>
                <div className="grid gap-6">
                    <div className="grid gap-2">
                        <Label htmlFor="email">Email address</Label>
                        <Input
                            id="email"
                            type="email"
                            required
                            autoFocus
                            tabIndex={1}
                            autoComplete="email"
                            value={data.email}
                            onChange={(e) => setData('email', e.target.value)}
                            disabled={processing}
                            placeholder="<EMAIL>"
                        />
                        <InputError message={errors.email} />
                    </div>

                    <Button type="submit" className="mt-2 w-full" tabIndex={2} disabled={processing}>
                        {processing && <LoaderCircle className="h-4 w-4 animate-spin" />}
                        Request OTP
                    </Button>
                </div>

                <div className="text-muted-foreground text-center text-sm">
                    Remember your password?{' '}
                    <TextLink href={route('login')} tabIndex={3}>
                        Log in
                    </TextLink>
                </div>
            </form>
        </AuthLayout>
    );
} 