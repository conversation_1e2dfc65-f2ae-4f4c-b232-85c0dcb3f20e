import { Head, useForm } from '@inertiajs/react';
import { LoaderCircle } from 'lucide-react';
import { FormEventHandler } from 'react';

import InputError from '@/components/input-error';
import TextLink from '@/components/text-link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AuthLayout from '@/layouts/auth-layout';

type OtpVerifyForm = {
    email: string;
    otp: string;
};

export default function OtpVerify() {
    const { data, setData, post, processing, errors, reset } = useForm<Required<OtpVerifyForm>>({
        email: '',
        otp: '',
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('otp.confirm'), {
            onFinish: () => reset('otp'),
        });
    };

    return (
        <AuthLayout title="Verify OTP" description="Enter the OTP sent to your email">
            <Head title="Verify OTP" />
            <form className="flex flex-col gap-6" onSubmit={submit}>
                <div className="grid gap-6">
                    <div className="grid gap-2">
                        <Label htmlFor="email">Email address</Label>
                        <Input
                            id="email"
                            type="email"
                            required
                            autoFocus
                            tabIndex={1}
                            autoComplete="email"
                            value={data.email}
                            onChange={(e) => setData('email', e.target.value)}
                            disabled={processing}
                            placeholder="<EMAIL>"
                        />
                        <InputError message={errors.email} />
                    </div>

                    <div className="grid gap-2">
                        <Label htmlFor="otp">OTP</Label>
                        <Input
                            id="otp"
                            type="text"
                            required
                            tabIndex={2}
                            value={data.otp}
                            onChange={(e) => setData('otp', e.target.value)}
                            disabled={processing}
                            placeholder="Enter OTP"
                        />
                        <InputError message={errors.otp} />
                    </div>

                    <Button type="submit" className="mt-2 w-full" tabIndex={3} disabled={processing}>
                        {processing && <LoaderCircle className="h-4 w-4 animate-spin" />}
                        Verify OTP
                    </Button>
                </div>

                <div className="text-muted-foreground text-center text-sm">
                    Didn't receive an OTP?{' '}
                    <TextLink href={route('otp.request')} tabIndex={4}>
                        Request again
                    </TextLink>
                </div>
            </form>
        </AuthLayout>
    );
} 