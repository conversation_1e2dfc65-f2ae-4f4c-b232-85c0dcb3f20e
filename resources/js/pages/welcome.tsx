import { type SharedData } from '@/types';
import { Head, Link, usePage } from '@inertiajs/react';
import {
    Users,
    BarChart3,
    User,
    Building2,
    CreditCard,
    Mail,
    MessageSquare,
    GanttChartSquare,
    CircleDollarSign,
    ReceiptText,
    Settings,
    LucideIcon,
    AlertCircle
} from 'lucide-react';

interface FeatureProps {
    icon: LucideIcon;
    title: string;
    description: string;
    color: string;
}

const Feature = ({ icon: Icon, title, description, color }: FeatureProps) => (
    <div className="flex flex-col gap-4 rounded-xl border p-6 transition-all hover:shadow-md dark:border-neutral-800">
        <div className={`rounded-lg p-2.5 w-fit ${color}`}>
            <Icon className="h-6 w-6" />
        </div>
        <div>
            <h3 className="mb-2 text-lg font-semibold">{title}</h3>
            <p className="text-muted-foreground text-sm">{description}</p>
        </div>
    </div>
);

interface RoleCardProps {
    icon: LucideIcon;
    title: string;
    description: string;
    abilities: string[];
    color: string;
}

const RoleCard = ({ icon: Icon, title, description, abilities, color }: RoleCardProps) => (
    <div className="flex flex-col rounded-xl border shadow-sm transition-all hover:shadow-md dark:border-neutral-800">
        <div className={`flex items-center gap-4 border-b p-5 dark:border-neutral-800 ${color}`}>
            <Icon className="h-8 w-8" />
            <div>
                <h3 className="text-xl font-semibold">{title}</h3>
                <p className="text-muted-foreground text-sm">{description}</p>
            </div>
        </div>
        <div className="p-5">
            <h4 className="mb-3 font-medium">Key Abilities:</h4>
            <ul className="space-y-2">
                {abilities.map((ability, index) => (
                    <li key={index} className="flex items-center gap-2 text-sm">
                        <svg className="h-4 w-4 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                        {ability}
                    </li>
                ))}
            </ul>
        </div>
    </div>
);

export default function Welcome() {
    const { auth } = usePage<SharedData>().props;
    const { adminExists } = usePage<{ adminExists: boolean }>().props;

    const features = [
        {
            icon: Users,
            title: "Multi-level User Management",
            description: "Full control over user hierarchy with Admin, Dealer, Retailer, Agent, and Customer roles.",
            color: "bg-blue-100 text-blue-600 dark:bg-blue-950 dark:text-blue-400"
        },
        {
            icon: Settings,
            title: "Powerful Admin Controls",
            description: "Comprehensive admin panel with role management and multi-admin settings.",
            color: "bg-purple-100 text-purple-600 dark:bg-purple-950 dark:text-purple-400"
        },
        {
            icon: BarChart3,
            title: "Transaction History",
            description: "Complete tracking and management of all transactions with detailed records.",
            color: "bg-orange-100 text-orange-600 dark:bg-orange-950 dark:text-orange-400"
        },
        {
            icon: Mail,
            title: "Mail Settings & Templates",
            description: "Configure SMTP/PHP Mail with customizable templates for OTP, info mails, and more.",
            color: "bg-green-100 text-green-600 dark:bg-green-950 dark:text-green-400"
        },
        {
            icon: CircleDollarSign,
            title: "Currency System",
            description: "Built-in currency conversion and support for multiple payment currencies.",
            color: "bg-amber-100 text-amber-600 dark:bg-amber-950 dark:text-amber-400"
        },
        {
            icon: CreditCard,
            title: "Payment Method Management",
            description: "Support for multiple payment methods with auto and manual payment gateways.",
            color: "bg-red-100 text-red-600 dark:bg-red-950 dark:text-red-400"
        },
        {
            icon: MessageSquare,
            title: "SMS Integration",
            description: "SMS settings and provider integrations for notifications and communications.",
            color: "bg-indigo-100 text-indigo-600 dark:bg-indigo-950 dark:text-indigo-400"
        },
        {
            icon: GanttChartSquare,
            title: "Flexible Addon Architecture",
            description: "Extend functionality with powerful addons and payment gateway integrations.",
            color: "bg-cyan-100 text-cyan-600 dark:bg-cyan-950 dark:text-cyan-400"
        }
    ];

    const roles = [
        {
            icon: User,
            title: "Admin",
            description: "Complete system control",
            abilities: [
                "Manage all users and roles",
                "Configure system settings",
                "Access transaction history",
                "Control payment methods",
                "Manage email/SMS templates",
                "View reports and analytics"
            ],
            color: "bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-950/30 dark:to-indigo-950/30"
        },
        {
            icon: Building2,
            title: "Dealer/Distributor",
            description: "High-level business partner",
            abilities: [
                "Use all core services",
                "Create Retailers/Agents/Customers",
                "Manage sub-accounts",
                "View transaction reports",
                "Process payments",
                "Configure account settings"
            ],
            color: "bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-950/30 dark:to-cyan-950/30"
        },
        {
            icon: ReceiptText,
            title: "Retailer",
            description: "Mid-level business partner",
            abilities: [
                "Use all core services",
                "Create Agents/Customers",
                "Manage limited sub-accounts",
                "Process customer payments",
                "View transaction history",
                "Configure own settings"
            ],
            color: "bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/30 dark:to-emerald-950/30"
        },
        {
            icon: User,
            title: "Agent & Customer",
            description: "Service users",
            abilities: [
                "Agents can create Customers",
                "Use all platform services",
                "Manage personal account",
                "View transaction history",
                "Process payments",
                "Self-registration available"
            ],
            color: "bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-950/30 dark:to-orange-950/30"
        }
    ];

    return (
        <>
            <Head title="iBilling - Complete Billing & User Management System">
                <link rel="preconnect" href="https://fonts.bunny.net" />
                <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />
            </Head>

            {!adminExists && (
                <div className="fixed inset-x-0 top-0 z-50">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="mt-4 rounded-lg bg-blue-50 p-4 dark:bg-blue-950">
                            <div className="flex">
                                <div className="flex-shrink-0">
                                    <AlertCircle className="h-5 w-5 text-blue-400" />
                                </div>
                                <div className="ml-3">
                                    <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200">
                                        Admin Account Required
                                    </h3>
                                    <div className="mt-2 text-sm text-blue-700 dark:text-blue-300">
                                        <p>
                                            No admin account has been created yet. Please create an admin account to get started.
                                        </p>
                                    </div>
                                    <div className="mt-4">
                                        <div className="-mx-2 -my-1.5 flex">
                                            <Link
                                                href={route('admin.setup')}
                                                className="rounded-md bg-blue-50 px-2 py-1.5 text-sm font-medium text-blue-800 hover:bg-blue-100 dark:bg-blue-900 dark:text-blue-200 dark:hover:bg-blue-800"
                                            >
                                                Create Admin Account
                                            </Link>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            <div className="flex min-h-screen flex-col bg-white text-gray-900 dark:bg-gray-950 dark:text-gray-100">
                {/* Header */}
                <header className="border-b dark:border-gray-800">
                    <div className="mx-auto flex h-16 max-w-7xl items-center justify-between px-4 sm:px-6 lg:px-8">
                        <div className="flex items-center">
                            <div className="text-2xl font-bold text-primary dark:text-white">
                                iBilling<span className="text-blue-600 dark:text-blue-500">Core</span>
                            </div>
                        </div>

                        <nav className="flex items-center gap-6">
                            <div className="hidden items-center gap-6 md:flex">
                                <a href="#features" className="text-sm font-medium hover:text-blue-600 dark:hover:text-blue-400">Features</a>
                                <a href="#roles" className="text-sm font-medium hover:text-blue-600 dark:hover:text-blue-400">User Roles</a>
                                <a href="#payments" className="text-sm font-medium hover:text-blue-600 dark:hover:text-blue-400">Payments</a>
                            </div>

                            <div className="flex items-center gap-4">
                                {auth.user ? (
                                    <Link
                                        href={route('dashboard')}
                                        className="rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600"
                                    >
                                        Dashboard
                                    </Link>
                                ) : (
                                    <>
                                        <Link
                                            href={route('login')}
                                            className="text-sm font-medium hover:text-blue-600 dark:hover:text-blue-400"
                                        >
                                            Log in
                                        </Link>
                                        <Link
                                            href={route('register')}
                                            className="rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600"
                                        >
                                            Register
                                        </Link>
                                    </>
                                )}
                            </div>
                        </nav>
                    </div>
                </header>

                <main className="flex-1">
                    {/* Hero Section */}
                    <section className="relative overflow-hidden bg-gradient-to-b from-white to-gray-50 px-4 pt-16 dark:from-gray-950 dark:to-gray-900 sm:px-6 lg:px-8 lg:pt-24">
                        <div className="mx-auto max-w-7xl">
                            <div className="lg:grid lg:grid-cols-12 lg:gap-12">
                                <div className="lg:col-span-6">
                                    <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl">
                                        Complete <span className="text-blue-600 dark:text-blue-500">Billing & User</span> Management System
                                    </h1>
                                    <p className="mt-6 text-lg text-gray-600 dark:text-gray-300">
                                        Powerful hierarchical user management with comprehensive billing, payment processing, 
                                        and communication tools for businesses of all sizes.
                                    </p>
                                    <div className="mt-8 flex flex-col gap-4 sm:flex-row">
                                        <Link
                                            href={route('register')}
                                            className="rounded-md bg-blue-600 px-6 py-3 text-center font-medium text-white shadow-sm hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600"
                                        >
                                            Get Started
                                        </Link>
                                        <a
                                            href="#features"
                                            className="rounded-md border border-gray-300 bg-white px-6 py-3 text-center font-medium text-gray-700 shadow-sm hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-200 dark:hover:bg-gray-800"
                                        >
                                            Explore Features
                                        </a>
                                    </div>
                                </div>
                                <div className="mt-12 lg:col-span-6 lg:mt-0">
                                    <div className="relative">
                                        <div className="aspect-[4/3] overflow-hidden rounded-xl bg-gradient-to-r from-blue-100 to-indigo-100 shadow-xl dark:from-blue-900/20 dark:to-indigo-900/20 lg:aspect-[5/3]">
                                            <div className="absolute inset-0 flex items-center justify-center">
                                                <div className="grid max-w-lg grid-cols-3 gap-4 p-4">
                                                    <div className="overflow-hidden rounded-lg bg-white shadow-md dark:bg-gray-800">
                                                        <div className="bg-blue-600 p-2 dark:bg-blue-700">
                                                            <div className="h-2 w-8 rounded-full bg-white/40"></div>
                                                        </div>
                                                        <div className="p-4">
                                                            <div className="space-y-2">
                                                                <div className="h-2 w-full rounded-full bg-gray-200 dark:bg-gray-700"></div>
                                                                <div className="h-2 w-2/3 rounded-full bg-gray-200 dark:bg-gray-700"></div>
                                                                <div className="mt-3 flex justify-between">
                                                                    <div className="h-6 w-16 rounded bg-blue-100 dark:bg-blue-900/30"></div>
                                                                    <div className="h-6 w-16 rounded bg-green-100 dark:bg-green-900/30"></div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div className="overflow-hidden rounded-lg bg-white shadow-md dark:bg-gray-800">
                                                        <div className="bg-purple-600 p-2 dark:bg-purple-700">
                                                            <div className="h-2 w-8 rounded-full bg-white/40"></div>
                                                        </div>
                                                        <div className="p-4">
                                                            <div className="space-y-2">
                                                                <div className="h-2 w-full rounded-full bg-gray-200 dark:bg-gray-700"></div>
                                                                <div className="h-2 w-3/4 rounded-full bg-gray-200 dark:bg-gray-700"></div>
                                                                <div className="mt-3 flex justify-between">
                                                                    <div className="h-6 w-16 rounded bg-purple-100 dark:bg-purple-900/30"></div>
                                                                    <div className="h-6 w-16 rounded bg-blue-100 dark:bg-blue-900/30"></div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div className="overflow-hidden rounded-lg bg-white shadow-md dark:bg-gray-800">
                                                        <div className="bg-emerald-600 p-2 dark:bg-emerald-700">
                                                            <div className="h-2 w-8 rounded-full bg-white/40"></div>
                                                        </div>
                                                        <div className="p-4">
                                                            <div className="space-y-2">
                                                                <div className="h-2 w-full rounded-full bg-gray-200 dark:bg-gray-700"></div>
                                                                <div className="h-2 w-1/2 rounded-full bg-gray-200 dark:bg-gray-700"></div>
                                                                <div className="mt-3 flex justify-between">
                                                                    <div className="h-6 w-16 rounded bg-emerald-100 dark:bg-emerald-900/30"></div>
                                                                    <div className="h-6 w-16 rounded bg-amber-100 dark:bg-amber-900/30"></div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div className="overflow-hidden rounded-lg bg-white shadow-md dark:bg-gray-800">
                                                        <div className="bg-amber-600 p-2 dark:bg-amber-700">
                                                            <div className="h-2 w-8 rounded-full bg-white/40"></div>
                                                        </div>
                                                        <div className="p-4">
                                                            <div className="space-y-2">
                                                                <div className="h-2 w-full rounded-full bg-gray-200 dark:bg-gray-700"></div>
                                                                <div className="h-2 w-3/5 rounded-full bg-gray-200 dark:bg-gray-700"></div>
                                                                <div className="mt-3 flex justify-between">
                                                                    <div className="h-6 w-16 rounded bg-amber-100 dark:bg-amber-900/30"></div>
                                                                    <div className="h-6 w-16 rounded bg-red-100 dark:bg-red-900/30"></div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div className="overflow-hidden rounded-lg bg-white shadow-md dark:bg-gray-800">
                                                        <div className="bg-red-600 p-2 dark:bg-red-700">
                                                            <div className="h-2 w-8 rounded-full bg-white/40"></div>
                                                        </div>
                                                        <div className="p-4">
                                                            <div className="space-y-2">
                                                                <div className="h-2 w-full rounded-full bg-gray-200 dark:bg-gray-700"></div>
                                                                <div className="h-2 w-4/5 rounded-full bg-gray-200 dark:bg-gray-700"></div>
                                                                <div className="mt-3 flex justify-between">
                                                                    <div className="h-6 w-16 rounded bg-red-100 dark:bg-red-900/30"></div>
                                                                    <div className="h-6 w-16 rounded bg-indigo-100 dark:bg-indigo-900/30"></div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div className="overflow-hidden rounded-lg bg-white shadow-md dark:bg-gray-800">
                                                        <div className="bg-indigo-600 p-2 dark:bg-indigo-700">
                                                            <div className="h-2 w-8 rounded-full bg-white/40"></div>
                                                        </div>
                                                        <div className="p-4">
                                                            <div className="space-y-2">
                                                                <div className="h-2 w-full rounded-full bg-gray-200 dark:bg-gray-700"></div>
                                                                <div className="h-2 w-2/3 rounded-full bg-gray-200 dark:bg-gray-700"></div>
                                                                <div className="mt-3 flex justify-between">
                                                                    <div className="h-6 w-16 rounded bg-indigo-100 dark:bg-indigo-900/30"></div>
                                                                    <div className="h-6 w-16 rounded bg-blue-100 dark:bg-blue-900/30"></div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="absolute inset-x-0 bottom-0 h-16 bg-gradient-to-t from-white dark:from-gray-950"></div>
                    </section>

                    {/* Key Features Section */}
                    <section id="features" className="py-16 sm:py-24">
                        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                            <div className="mx-auto max-w-2xl text-center">
                                <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">Powerful Features for Your Business</h2>
                                <p className="mt-4 text-gray-600 dark:text-gray-300">
                                    iBilling provides all the tools you need to manage users, process payments, and grow your business.
                                </p>
                            </div>
                            <div className="mt-16 grid gap-8 sm:grid-cols-2 lg:grid-cols-4">
                                {features.map((feature, index) => (
                                    <Feature
                                        key={index}
                                        icon={feature.icon}
                                        title={feature.title}
                                        description={feature.description}
                                        color={feature.color}
                                    />
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* User Roles Section */}
                    <section id="roles" className="bg-gray-50 py-16 sm:py-24 dark:bg-gray-900">
                        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                            <div className="mx-auto max-w-2xl text-center">
                                <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">Hierarchical User Management</h2>
                                <p className="mt-4 text-gray-600 dark:text-gray-300">
                                    Flexible user roles with cascading permissions for complete business control.
                                </p>
                            </div>
                            <div className="mt-16 grid gap-8 sm:grid-cols-2">
                                {roles.map((role, index) => (
                                    <RoleCard
                                        key={index}
                                        icon={role.icon}
                                        title={role.title}
                                        description={role.description}
                                        abilities={role.abilities}
                                        color={role.color}
                                    />
                                ))}
                            </div>
                        </div>
                    </section>

                    {/* Payment Section */}
                    <section id="payments" className="py-16 sm:py-24">
                        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                            <div className="mx-auto max-w-2xl text-center">
                                <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">Versatile Payment Solutions</h2>
                                <p className="mt-4 text-gray-600 dark:text-gray-300">
                                    Support for multiple payment methods and currencies with automated and manual gateway options.
                                </p>
                            </div>
                            <div className="mt-16 overflow-hidden rounded-xl bg-white shadow-lg dark:bg-gray-900 dark:border dark:border-gray-800">
                                <div className="grid gap-0 md:grid-cols-3">
                                    <div className="border-b p-8 md:border-b-0 md:border-r dark:border-gray-800">
                                        <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900/30">
                                            <CreditCard className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                                        </div>
                                        <h3 className="mt-4 text-xl font-semibold">Payment Gateways</h3>
                                        <p className="mt-2 text-sm text-gray-600 dark:text-gray-300">
                                            Support for automatic payment gateways via addons and manual payment processing. Easily extendable architecture.
                                        </p>
                                    </div>
                                    <div className="border-b p-8 md:border-b-0 md:border-r dark:border-gray-800">
                                        <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-100 dark:bg-green-900/30">
                                            <CircleDollarSign className="h-6 w-6 text-green-600 dark:text-green-400" />
                                        </div>
                                        <h3 className="mt-4 text-xl font-semibold">Multi-Currency</h3>
                                        <p className="mt-2 text-sm text-gray-600 dark:text-gray-300">
                                            Built-in currency conversion system. Support for multiple currencies with automatic exchange rate updates.
                                        </p>
                                    </div>
                                    <div className="p-8">
                                        <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-purple-100 dark:bg-purple-900/30">
                                            <BarChart3 className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                                        </div>
                                        <h3 className="mt-4 text-xl font-semibold">Transaction Management</h3>
                                        <p className="mt-2 text-sm text-gray-600 dark:text-gray-300">
                                            Complete transaction history with detailed reports. Monitor and manage all payment activities.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                    {/* CTA Section */}
                    <section className="bg-blue-600 py-16 sm:py-24 dark:bg-blue-900">
                        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                            <div className="mx-auto max-w-2xl text-center">
                                <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
                                    Ready to Transform Your Business?
                                </h2>
                                <p className="mt-4 text-lg text-blue-100">
                                    Get started with iBilling today and streamline your user and payment management.
                                </p>
                                <div className="mt-8 flex justify-center gap-4">
                                    <Link
                                        href={route('register')}
                                        className="rounded-md bg-white px-6 py-3 text-center font-medium text-blue-600 shadow-sm hover:bg-blue-50"
                                    >
                                        Start Now
                                    </Link>
                                    <a
                                        href="#features"
                                        className="rounded-md border border-blue-300 px-6 py-3 text-center font-medium text-white shadow-sm hover:bg-blue-700"
                                    >
                                        Learn More
                                    </a>
                                </div>
                            </div>
                        </div>
                    </section>
                </main>

                {/* Footer */}
                <footer className="border-t dark:border-gray-800">
                    <div className="mx-auto max-w-7xl px-4 py-12 sm:px-6 lg:px-8">
                        <div className="md:flex md:items-center md:justify-between">
                            <div className="flex justify-center space-x-6 md:order-2">
                                <div className="text-sm text-gray-500 dark:text-gray-400">
                                    <a href="#features" className="mr-4 hover:text-blue-600 dark:hover:text-blue-400">Features</a>
                                    <a href="#roles" className="mr-4 hover:text-blue-600 dark:hover:text-blue-400">User Roles</a>
                                    <a href="#payments" className="hover:text-blue-600 dark:hover:text-blue-400">Payments</a>
                                </div>
                            </div>
                            <div className="mt-8 md:order-1 md:mt-0">
                                <p className="text-center text-sm text-gray-500 dark:text-gray-400">
                                    &copy; {new Date().getFullYear()} iBilling Core. All rights reserved.
                                </p>
                            </div>
                        </div>
                    </div>
                </footer>
            </div>
        </>
    );
}
