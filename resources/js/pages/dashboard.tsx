import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { 
    Users, 
    UserPlus, 
    Settings, 
    BarChart3, 
    CreditCard, 
    DollarSign, 
    UserCog,
    ArrowUpRight,
    ArrowDownRight
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
];

interface User {
    id: number;
    name: string;
    email: string;
    role: {
        id: number;
        name: string;
        slug: string;
    };
    balance: number;
    children_count: number;
}

interface Props {
    user: User;
    stats: {
        total_users: number;
        total_transactions: number;
        total_balance: number;
        recent_transactions: Array<{
            id: number;
            type: 'credit' | 'debit';
            amount: number;
            description: string;
            created_at: string;
        }>;
    };
}

export default function Dashboard({ user, stats }: Props) {
    const canManageUsers = ['super-admin', 'admin', 'dealer', 'retailer', 'agent'].includes(user.role.slug);
    const canManageCommissions = ['super-admin', 'admin', 'dealer', 'retailer'].includes(user.role.slug);
    const canViewReports = ['super-admin', 'admin', 'dealer'].includes(user.role.slug);

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Dashboard" />
            <div className="flex h-full flex-1 flex-col gap-4 p-4">
                {/* Welcome Section */}
                <div className="rounded-xl border bg-card p-6">
                    <h1 className="text-2xl font-semibold">Welcome back, {user.name}!</h1>
                    <p className="text-muted-foreground mt-1">Here's what's happening with your account today.</p>
                </div>

                {/* Quick Stats */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Balance</CardTitle>
                            <DollarSign className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">${Number(user.balance).toFixed(2)}</div>
                            <p className="text-xs text-muted-foreground">
                                Your current account balance
                            </p>
                        </CardContent>
                    </Card>

                    {canManageUsers && (
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Sub-accounts</CardTitle>
                                <Users className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stats.total_users}</div>
                                <p className="text-xs text-muted-foreground">
                                    Total users under your hierarchy
                                </p>
                            </CardContent>
                        </Card>
                    )}

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Transactions</CardTitle>
                            <CreditCard className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_transactions}</div>
                            <p className="text-xs text-muted-foreground">
                                Total transactions processed
                            </p>
                        </CardContent>
                    </Card>

                    {canViewReports && (
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                                <BarChart3 className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">${Number(stats.total_balance).toFixed(2)}</div>
                                <p className="text-xs text-muted-foreground">
                                    Total revenue in your hierarchy
                                </p>
                            </CardContent>
                        </Card>
                    )}
                </div>

                {/* Quick Actions */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {canManageUsers && (
                        <Card>
                            <CardHeader>
                                <CardTitle>User Management</CardTitle>
                                <CardDescription>Manage your sub-accounts and users</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <Button asChild className="w-full">
                                    <Link href={route('users.create')}>
                                        <UserPlus className="mr-2 h-4 w-4" />
                                        Create New User
                                    </Link>
                                </Button>
                                <Button asChild variant="outline" className="w-full">
                                    <Link href={route('users.index')}>
                                        <Users className="mr-2 h-4 w-4" />
                                        View All Users
                                    </Link>
                                </Button>
                            </CardContent>
                        </Card>
                    )}

                    {canManageCommissions && (
                        <Card>
                            <CardHeader>
                                <CardTitle>Commission Management</CardTitle>
                                <CardDescription>Set and manage commission rates</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <Button asChild className="w-full">
                                    <Link href={route('commissions.create')}>
                                        <DollarSign className="mr-2 h-4 w-4" />
                                        Set Commission Rate
                                    </Link>
                                </Button>
                                <Button asChild variant="outline" className="w-full">
                                    <Link href={route('commissions.index')}>
                                        <Settings className="mr-2 h-4 w-4" />
                                        Manage Commissions
                                    </Link>
                                </Button>
                            </CardContent>
                        </Card>
                    )}

                    <Card>
                        <CardHeader>
                            <CardTitle>Account Settings</CardTitle>
                            <CardDescription>Manage your account preferences</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <Button asChild className="w-full">
                                <Link href={route('profile.edit')}>
                                    <UserCog className="mr-2 h-4 w-4" />
                                    Edit Profile
                                </Link>
                            </Button>
                            <Button asChild variant="outline" className="w-full">
                                <Link href={route('profile.edit')}>
                                    <Settings className="mr-2 h-4 w-4" />
                                    Account Settings
                                </Link>
                            </Button>
                        </CardContent>
                    </Card>
                </div>

                {/* Recent Transactions */}
                <Card>
                    <CardHeader>
                        <CardTitle>Recent Transactions</CardTitle>
                        <CardDescription>Your latest transaction history</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {stats.recent_transactions.map((transaction) => (
                                <div key={transaction.id} className="flex items-center justify-between">
                                    <div className="flex items-center gap-4">
                                        <div className={`rounded-full p-2 ${
                                            transaction.type === 'credit' 
                                                ? 'bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400'
                                                : 'bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-400'
                                        }`}>
                                            {transaction.type === 'credit' ? (
                                                <ArrowUpRight className="h-4 w-4" />
                                            ) : (
                                                <ArrowDownRight className="h-4 w-4" />
                                            )}
                                        </div>
                                        <div>
                                            <p className="font-medium">{transaction.description}</p>
                                            <p className="text-sm text-muted-foreground">
                                                {new Date(transaction.created_at).toLocaleDateString()}
                                            </p>
                                        </div>
                                    </div>
                                    <div className={`font-medium ${
                                        transaction.type === 'credit' 
                                            ? 'text-green-600 dark:text-green-400'
                                            : 'text-red-600 dark:text-red-400'
                                    }`}>
                                        {transaction.type === 'credit' ? '+' : '-'}${transaction.amount.toFixed(2)}
                                    </div>
                                </div>
                            ))}
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
