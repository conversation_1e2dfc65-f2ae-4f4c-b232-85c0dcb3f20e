import { NavFooter } from '@/components/nav-footer';
import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import { BookOpen, Folder, LayoutGrid, Users, DollarSign, Settings, ShieldCheck, Package, Shield, Lock } from 'lucide-react';
import AppLogo from './app-logo';

// Icon mapping for dynamic navigation
const iconMap = {
    LayoutGrid,
    Users,
    DollarSign,
    Package,
    Settings,
    ShieldCheck,
    Shield,
    Lock,
    Folder,
    BookOpen,
};

export function AppSidebar() {
    const { navigation } = usePage().props as any;
    return (
        <Sidebar collapsible="icon" variant="inset">
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <Link href="/dashboard" prefetch>
                                <AppLogo />
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent>
                <NavMain items={navigation?.main?.map((item: any) => ({
                    ...item,
                    icon: iconMap[item.icon as keyof typeof iconMap]
                })) || []} />
            </SidebarContent>

            <SidebarFooter>
                <NavFooter items={navigation?.footer?.map((item: any) => ({
                    ...item,
                    icon: iconMap[item.icon as keyof typeof iconMap]
                })) || []} className="mt-auto" />
                <NavUser />
            </SidebarFooter>
        </Sidebar>
    );
}
