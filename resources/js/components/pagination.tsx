import { Link } from '@inertiajs/react';
import { ChevronLeft, ChevronRight } from 'lucide-react';

import { Button } from '@/components/ui/button';

interface Link {
    url: string | null;
    label: string;
    active: boolean;
}

interface Props {
    links: Link[];
}

export function Pagination({ links }: Props) {
    return (
        <div className="flex items-center justify-center gap-2">
            {links.map((link, i) => {
                if (i === 0) {
                    return (
                        <Button
                            key="prev"
                            variant="outline"
                            size="icon"
                            disabled={!link.url}
                            asChild={!!link.url}
                        >
                            {link.url ? (
                                <Link href={link.url}>
                                    <ChevronLeft className="h-4 w-4" />
                                </Link>
                            ) : (
                                <ChevronLeft className="h-4 w-4" />
                            )}
                        </Button>
                    );
                }

                if (i === links.length - 1) {
                    return (
                        <Button
                            key="next"
                            variant="outline"
                            size="icon"
                            disabled={!link.url}
                            asChild={!!link.url}
                        >
                            {link.url ? (
                                <Link href={link.url}>
                                    <ChevronRight className="h-4 w-4" />
                                </Link>
                            ) : (
                                <ChevronRight className="h-4 w-4" />
                            )}
                        </Button>
                    );
                }

                return (
                    <Button
                        key={i}
                        variant={link.active ? 'default' : 'outline'}
                        size="icon"
                        asChild={!!link.url}
                    >
                        {link.url ? (
                            <Link href={link.url}>{link.label}</Link>
                        ) : (
                            <span>{link.label}</span>
                        )}
                    </Button>
                );
            })}
        </div>
    );
} 