<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Http\Controllers\UserController;
use App\Http\Controllers\AdminSetupController;
use App\Models\User;
use App\Models\Role;
use App\Http\Controllers\CommissionController;
use App\Http\Controllers\Admin\OtpSettingsController;
use App\Http\Controllers\Admin\ModuleController;
use App\Http\Controllers\Admin\RoleController;
use App\Http\Controllers\Admin\PagePermissionController;

Route::get('/', function () {
    $adminRole = Role::where('slug', 'admin')->first();
    $adminExists = User::where('role_id', $adminRole->id)->exists();

    return Inertia::render('welcome', [
        'adminExists' => $adminExists
    ]);
})->name('home');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('/dashboard', function () {
        $user = auth()->user()->load('role');

        // Get user stats
        $stats = [
            'total_users' => $user->children()->count(),
            'total_transactions' => $user->transactions()->count(),
            'total_balance' => $user->transactions()
                ->where('status', 'completed')
                ->sum(\DB::raw("CASE WHEN type = 'credit' THEN amount ELSE -amount END")),
            'recent_transactions' => $user->transactions()
                ->latest()
                ->take(5)
                ->get()
                ->map(function ($transaction) {
                    return [
                        'id' => $transaction->id,
                        'type' => $transaction->type,
                        'amount' => $transaction->amount,
                        'description' => $transaction->description,
                        'created_at' => $transaction->created_at,
                    ];
                })
        ];

        return Inertia::render('dashboard', [
            'user' => $user,
            'stats' => $stats
        ]);
    })->name('dashboard');

    // User Management Routes
    Route::resource('users', UserController::class);

    // Commission Management Routes
    Route::get('/commissions', [CommissionController::class, 'index'])->name('commissions.index');
    Route::get('/commissions/create', [CommissionController::class, 'create'])->name('commissions.create');
    Route::post('/commissions', [CommissionController::class, 'store'])->name('commissions.store');
    Route::get('/commissions/{commission}/edit', [CommissionController::class, 'edit'])->name('commissions.edit');
    Route::put('/commissions/{commission}', [CommissionController::class, 'update'])->name('commissions.update');
    Route::delete('/commissions/{commission}', [CommissionController::class, 'destroy'])->name('commissions.destroy');

    // Module Management Routes
    Route::middleware(['auth'])->prefix('admin')->name('admin.')->group(function () {
        Route::get('/modules', [ModuleController::class, 'index'])->name('modules.index');
        Route::post('/modules', [ModuleController::class, 'store'])->name('modules.store');
        Route::post('/modules/refresh', [ModuleController::class, 'refresh'])->name('modules.refresh');
        Route::post('/modules/{module}/activate', [ModuleController::class, 'activate'])->name('modules.activate');
        Route::post('/modules/{module}/deactivate', [ModuleController::class, 'deactivate'])->name('modules.deactivate');
        Route::delete('/modules/{module}', [ModuleController::class, 'destroy'])->name('modules.destroy');
    });

    // OTP Settings Routes
    Route::prefix('admin/settings')->name('admin.settings.')->group(function () {
        Route::get('/otp', [OtpSettingsController::class, 'index'])->name('otp.index');
        Route::put('/otp', [OtpSettingsController::class, 'updateOtpSettings'])->name('otp.update');
        Route::put('/otp/templates/{template}', [OtpSettingsController::class, 'updateTemplate'])->name('otp.templates.update');
        Route::put('/smtp', [OtpSettingsController::class, 'updateSmtpSettings'])->name('smtp.update');
        Route::post('/smtp/test', [OtpSettingsController::class, 'testSmtpConnection'])->name('smtp.test');
        Route::post('/otp/create-default-templates', [OtpSettingsController::class, 'createDefaultTemplates'])
            ->name('otp.create-default-templates');
    });

    // Role Management Routes (Super Admin only)
    Route::middleware(['role:super-admin'])->group(function () {
        Route::resource('roles', RoleController::class)->names('admin.roles');
        Route::resource('page-permissions', PagePermissionController::class)->names('admin.page-permissions');
        Route::post('page-permissions/{pagePermission}/toggle', [PagePermissionController::class, 'toggle'])
            ->name('admin.page-permissions.toggle');
    });
});

// Admin Setup Routes
Route::get('/admin-setup', [AdminSetupController::class, 'create'])->name('admin.setup');
Route::post('/admin-setup', [AdminSetupController::class, 'store'])->name('admin.setup.store');

require __DIR__ . '/settings.php';
require __DIR__ . '/auth.php';
