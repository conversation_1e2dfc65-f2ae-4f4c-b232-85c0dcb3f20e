<?php

namespace Database\Seeders;

use App\Models\OtpTemplate;
use Illuminate\Database\Seeder;

class OtpTemplateSeeder extends Seeder
{
    public function run()
    {
        $templates = [
            [
                'type' => 'email',
                'name' => 'Email OTP Verification',
                'subject' => 'Your OTP Verification Code',
                'body' => "Hello {name},\n\nYour OTP verification code is: {otp}\n\nThis code will expire in {expiry_time} minutes.\n\nIf you didn't request this code, please ignore this email.\n\nBest regards,\nYour App Team",
                'variables' => ['{name}', '{otp}', '{expiry_time}'],
                'is_active' => true,
            ],
            [
                'type' => 'sms',
                'name' => 'SMS OTP Verification',
                'body' => "Your OTP verification code is: {otp}. This code will expire in {expiry_time} minutes.",
                'variables' => ['{otp}', '{expiry_time}'],
                'is_active' => true,
            ],
            [
                'type' => 'whatsapp',
                'name' => 'WhatsApp OTP Verification',
                'body' => "Hello {name},\n\nYour OTP verification code is: {otp}\n\nThis code will expire in {expiry_time} minutes.\n\nIf you didn't request this code, please ignore this message.",
                'variables' => ['{name}', '{otp}', '{expiry_time}'],
                'is_active' => true,
            ],
            [
                'type' => 'telegram',
                'name' => 'Telegram OTP Verification',
                'body' => "Hello {name},\n\nYour OTP verification code is: {otp}\n\nThis code will expire in {expiry_time} minutes.\n\nIf you didn't request this code, please ignore this message.",
                'variables' => ['{name}', '{otp}', '{expiry_time}'],
                'is_active' => true,
            ],
        ];

        foreach ($templates as $template) {
            OtpTemplate::create($template);
        }
    }
} 