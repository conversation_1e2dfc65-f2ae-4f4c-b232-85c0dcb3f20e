<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('email_templates', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->string('subject');
            $table->text('body');
            $table->json('variables')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            $table->softDeletes();
        });

        // Insert default email templates
        DB::table('email_templates')->insert([
            [
                'name' => 'Welcome Email',
                'slug' => 'welcome-email',
                'subject' => 'Welcome to {site_name}',
                'body' => "Hello {user_name},\n\nWelcome to {site_name}! We're excited to have you on board.\n\nBest regards,\n{site_name} Team",
                'variables' => json_encode(['site_name', 'user_name']),
                'is_active' => true
            ],
            [
                'name' => 'OTP Email',
                'slug' => 'otp-email',
                'subject' => 'Your OTP Code',
                'body' => "Hello {user_name},\n\nYour OTP code is: {otp_code}\n\nThis code will expire in {expiry_time} minutes.\n\nBest regards,\n{site_name} Team",
                'variables' => json_encode(['user_name', 'otp_code', 'expiry_time', 'site_name']),
                'is_active' => true
            ],
            [
                'name' => 'Password Reset',
                'slug' => 'password-reset',
                'subject' => 'Password Reset Request',
                'body' => "Hello {user_name},\n\nYou have requested to reset your password. Click the link below to reset your password:\n\n{reset_link}\n\nThis link will expire in {expiry_time} minutes.\n\nBest regards,\n{site_name} Team",
                'variables' => json_encode(['user_name', 'reset_link', 'expiry_time', 'site_name']),
                'is_active' => true
            ]
        ]);
    }

    public function down(): void
    {
        Schema::dropIfExists('email_templates');
    }
}; 