<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up(): void
    {
        // Create permissions table
        Schema::create('permissions', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->string('slug')->unique();
            $table->string('description')->nullable();
            $table->string('category')->default('general');
            $table->timestamps();
        });

        // Create role_permissions pivot table
        Schema::create('role_permissions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('role_id')->constrained()->onDelete('cascade');
            $table->foreignId('permission_id')->constrained()->onDelete('cascade');
            $table->timestamps();
            
            $table->unique(['role_id', 'permission_id']);
        });

        // Create page_permissions table for managing page access
        Schema::create('page_permissions', function (Blueprint $table) {
            $table->id();
            $table->string('page_name');
            $table->string('page_route');
            $table->string('page_url');
            $table->json('required_permissions')->nullable();
            $table->json('required_roles')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->unique('page_route');
        });

        // Insert default permissions
        $permissions = [
            // Dashboard permissions
            ['name' => 'View Dashboard', 'slug' => 'view-dashboard', 'description' => 'Access to dashboard', 'category' => 'dashboard'],
            
            // User management permissions
            ['name' => 'View Users', 'slug' => 'view-users', 'description' => 'View user list', 'category' => 'users'],
            ['name' => 'Create Users', 'slug' => 'create-users', 'description' => 'Create new users', 'category' => 'users'],
            ['name' => 'Edit Users', 'slug' => 'edit-users', 'description' => 'Edit existing users', 'category' => 'users'],
            ['name' => 'Delete Users', 'slug' => 'delete-users', 'description' => 'Delete users', 'category' => 'users'],
            
            // Commission management permissions
            ['name' => 'View Commissions', 'slug' => 'view-commissions', 'description' => 'View commission list', 'category' => 'commissions'],
            ['name' => 'Create Commissions', 'slug' => 'create-commissions', 'description' => 'Create new commissions', 'category' => 'commissions'],
            ['name' => 'Edit Commissions', 'slug' => 'edit-commissions', 'description' => 'Edit existing commissions', 'category' => 'commissions'],
            ['name' => 'Delete Commissions', 'slug' => 'delete-commissions', 'description' => 'Delete commissions', 'category' => 'commissions'],
            
            // Module management permissions
            ['name' => 'View Modules', 'slug' => 'view-modules', 'description' => 'View module list', 'category' => 'modules'],
            ['name' => 'Manage Modules', 'slug' => 'manage-modules', 'description' => 'Install, activate, deactivate modules', 'category' => 'modules'],
            
            // Settings permissions
            ['name' => 'View Settings', 'slug' => 'view-settings', 'description' => 'Access settings pages', 'category' => 'settings'],
            ['name' => 'Manage OTP Settings', 'slug' => 'manage-otp-settings', 'description' => 'Manage OTP and SMTP settings', 'category' => 'settings'],
            
            // Role management permissions (Super Admin only)
            ['name' => 'Manage Roles', 'slug' => 'manage-roles', 'description' => 'Manage roles and permissions', 'category' => 'administration'],
            ['name' => 'Manage Page Permissions', 'slug' => 'manage-page-permissions', 'description' => 'Manage page access permissions', 'category' => 'administration'],
        ];

        foreach ($permissions as $permission) {
            $permission['created_at'] = now();
            $permission['updated_at'] = now();
        }

        DB::table('permissions')->insert($permissions);

        // Insert default page permissions
        $pages = [
            ['page_name' => 'Dashboard', 'page_route' => 'dashboard', 'page_url' => '/dashboard', 'required_permissions' => json_encode(['view-dashboard'])],
            ['page_name' => 'User Management', 'page_route' => 'users.index', 'page_url' => '/users', 'required_permissions' => json_encode(['view-users'])],
            ['page_name' => 'Create User', 'page_route' => 'users.create', 'page_url' => '/users/create', 'required_permissions' => json_encode(['create-users'])],
            ['page_name' => 'Commission Management', 'page_route' => 'commissions.index', 'page_url' => '/commissions', 'required_permissions' => json_encode(['view-commissions'])],
            ['page_name' => 'Module Management', 'page_route' => 'admin.modules.index', 'page_url' => '/admin/modules', 'required_permissions' => json_encode(['view-modules'])],
            ['page_name' => 'Settings', 'page_route' => 'settings.profile', 'page_url' => '/settings', 'required_permissions' => json_encode(['view-settings'])],
            ['page_name' => 'OTP Settings', 'page_route' => 'admin.settings.otp.index', 'page_url' => '/admin/settings/otp', 'required_permissions' => json_encode(['manage-otp-settings'])],
            ['page_name' => 'Role Management', 'page_route' => 'admin.roles.index', 'page_url' => '/admin/roles', 'required_roles' => json_encode(['super-admin'])],
            ['page_name' => 'Page Permissions', 'page_route' => 'admin.page-permissions.index', 'page_url' => '/admin/page-permissions', 'required_roles' => json_encode(['super-admin'])],
        ];

        foreach ($pages as $page) {
            $page['created_at'] = now();
            $page['updated_at'] = now();
        }

        DB::table('page_permissions')->insert($pages);
    }

    public function down(): void
    {
        Schema::dropIfExists('page_permissions');
        Schema::dropIfExists('role_permissions');
        Schema::dropIfExists('permissions');
    }
};
