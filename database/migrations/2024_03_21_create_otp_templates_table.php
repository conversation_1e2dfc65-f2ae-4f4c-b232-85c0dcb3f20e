<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('otp_templates', function (Blueprint $table) {
            $table->id();
            $table->string('type'); // email, sms, whatsapp, telegram
            $table->string('name');
            $table->text('subject')->nullable();
            $table->text('body');
            $table->json('variables')->nullable(); // Store available variables for template
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('otp_templates');
    }
}; 