<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use App\Models\Role;
use App\Models\Permission;

return new class extends Migration
{
    public function up(): void
    {
        // Assign permissions to roles
        $this->assignPermissionsToRoles();
    }

    private function assignPermissionsToRoles()
    {
        $roles = Role::all();
        $permissions = Permission::all()->keyBy('slug');

        foreach ($roles as $role) {
            $rolePermissions = [];

            switch ($role->slug) {
                case 'super-admin':
                    // Super Admin gets all permissions
                    $rolePermissions = $permissions->pluck('id')->toArray();
                    break;

                case 'admin':
                    // Admin gets most permissions except role management
                    $rolePermissions = $permissions->whereNotIn('slug', [
                        'manage-roles',
                        'manage-page-permissions'
                    ])->pluck('id')->toArray();
                    break;

                case 'dealer':
                    // Dealer gets user and commission management
                    $rolePermissions = $permissions->whereIn('slug', [
                        'view-dashboard',
                        'view-users',
                        'create-users',
                        'edit-users',
                        'view-commissions',
                        'create-commissions',
                        'edit-commissions',
                        'view-settings'
                    ])->pluck('id')->toArray();
                    break;

                case 'retailer':
                    // Retailer gets limited user management
                    $rolePermissions = $permissions->whereIn('slug', [
                        'view-dashboard',
                        'view-users',
                        'create-users',
                        'edit-users',
                        'view-commissions',
                        'view-settings'
                    ])->pluck('id')->toArray();
                    break;

                case 'agent':
                    // Agent gets basic access
                    $rolePermissions = $permissions->whereIn('slug', [
                        'view-dashboard',
                        'view-users',
                        'create-users',
                        'view-settings'
                    ])->pluck('id')->toArray();
                    break;

                case 'customer':
                    // Customer gets minimal access
                    $rolePermissions = $permissions->whereIn('slug', [
                        'view-dashboard',
                        'view-settings'
                    ])->pluck('id')->toArray();
                    break;
            }

            if (!empty($rolePermissions)) {
                $role->permissions()->sync($rolePermissions);
            }
        }
    }

    public function down(): void
    {
        // Remove all role-permission relationships
        DB::table('role_permissions')->truncate();
    }
};
