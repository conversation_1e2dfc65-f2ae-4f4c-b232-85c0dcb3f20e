<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('roles', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->json('permissions')->nullable();
            $table->timestamps();
        });

        // Insert default roles
        DB::table('roles')->insert([
            [
                'name' => 'Super Admin',
                'slug' => 'super-admin',
                'description' => 'Super Administrator with all permissions',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Admin',
                'slug' => 'admin',
                'description' => 'Administrator with limited permissions',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Dealer',
                'slug' => 'dealer',
                'description' => 'Dealer/Distributor role',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Retailer',
                'slug' => 'retailer',
                'description' => 'Retailer role',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Agent',
                'slug' => 'agent',
                'description' => 'Agent role',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Customer',
                'slug' => 'customer',
                'description' => 'Customer role',
                'created_at' => now(),
                'updated_at' => now()
            ],
        ]);
    }

    public function down(): void
    {
        Schema::dropIfExists('roles');
    }
};