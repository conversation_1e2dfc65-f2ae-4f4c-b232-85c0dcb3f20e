<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('otp_settings', function (Blueprint $table) {
            $table->id();
            $table->boolean('email_otp_enabled')->default(false);
            $table->boolean('phone_otp_enabled')->default(false);
            $table->boolean('whatsapp_otp_enabled')->default(false);
            $table->boolean('telegram_otp_enabled')->default(false);
            $table->boolean('email_verification_required')->default(false);
            $table->boolean('phone_verification_required')->default(false);
            $table->boolean('whatsapp_verification_required')->default(false);
            $table->boolean('telegram_verification_required')->default(false);
            $table->integer('otp_expiry_minutes')->default(10);
            $table->integer('max_otp_attempts')->default(3);
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('otp_settings');
    }
}; 