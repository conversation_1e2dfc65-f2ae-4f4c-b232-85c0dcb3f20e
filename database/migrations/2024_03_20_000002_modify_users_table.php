<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->foreignId('role_id')->after('id')->constrained('roles');
            $table->foreignId('parent_id')->nullable()->after('role_id')->constrained('users');
            $table->string('phone')->nullable()->unique();
            $table->string('whatsapp')->nullable();
            $table->string('telegram')->nullable();
            $table->decimal('balance', 15, 2)->default(0);
            $table->boolean('is_active')->default(true);
            $table->json('settings')->nullable();
            $table->timestamp('last_login_at')->nullable();
            $table->string('last_login_ip')->nullable();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['role_id']);
            $table->dropForeign(['parent_id']);
            $table->dropColumn([
                'role_id',
                'parent_id',
                'phone',
                'whatsapp',
                'telegram',
                'balance',
                'is_active',
                'settings',
                'last_login_at',
                'last_login_ip'
            ]);
            $table->dropSoftDeletes();
        });
    }
}; 