<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->id();
            $table->string('group'); // mail, sms, payment, currency, etc.
            $table->string('key');
            $table->text('value')->nullable();
            $table->string('type')->default('string'); // string, integer, boolean, json, etc.
            $table->boolean('is_public')->default(false);
            $table->timestamps();

            $table->unique(['group', 'key']);
        });

        // Insert default settings
        DB::table('settings')->insert([
            // Mail Settings
            ['group' => 'mail', 'key' => 'driver', 'value' => 'smtp', 'type' => 'string'],
            ['group' => 'mail', 'key' => 'host', 'value' => '', 'type' => 'string'],
            ['group' => 'mail', 'key' => 'port', 'value' => '587', 'type' => 'integer'],
            ['group' => 'mail', 'key' => 'username', 'value' => '', 'type' => 'string'],
            ['group' => 'mail', 'key' => 'password', 'value' => '', 'type' => 'string'],
            ['group' => 'mail', 'key' => 'encryption', 'value' => 'tls', 'type' => 'string'],
            ['group' => 'mail', 'key' => 'from_address', 'value' => '', 'type' => 'string'],
            ['group' => 'mail', 'key' => 'from_name', 'value' => '', 'type' => 'string'],

            // SMS Settings
            ['group' => 'sms', 'key' => 'provider', 'value' => '', 'type' => 'string'],
            ['group' => 'sms', 'key' => 'api_key', 'value' => '', 'type' => 'string'],
            ['group' => 'sms', 'key' => 'api_secret', 'value' => '', 'type' => 'string'],
            ['group' => 'sms', 'key' => 'from', 'value' => '', 'type' => 'string'],

            // Currency Settings
            ['group' => 'currency', 'key' => 'default', 'value' => 'USD', 'type' => 'string'],
            ['group' => 'currency', 'key' => 'exchange_rates', 'value' => '{}', 'type' => 'json'],

            // Payment Settings
            ['group' => 'payment', 'key' => 'default_gateway', 'value' => '', 'type' => 'string'],
            ['group' => 'payment', 'key' => 'supported_gateways', 'value' => '[]', 'type' => 'json'],
        ]);
    }

    public function down(): void
    {
        Schema::dropIfExists('settings');
    }
}; 