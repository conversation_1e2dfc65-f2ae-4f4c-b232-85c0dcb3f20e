<?php

namespace Modules\Irecharge\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Modules\Irecharge\Models\MobileRecharge;
use Illuminate\Support\Facades\Auth;

class MobileRechargeController extends Controller
{
    public function index()
    {
        $recharges = MobileRecharge::where('user_id', Auth::id())
            ->latest()
            ->paginate(10);

        return inertia('irecharge::index', [
            'recharges' => $recharges
        ]);
    }

    public function create()
    {
        return inertia('irecharge::create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'mobile_number' => 'required|string|min:10|max:15',
            'provider' => 'required|string',
            'amount' => 'required|numeric|min:1',
        ]);

        $recharge = MobileRecharge::create([
            'user_id' => Auth::id(),
            'mobile_number' => $request->mobile_number,
            'provider' => $request->provider,
            'amount' => $request->amount,
            'status' => 'pending',
        ]);

        // Here you would typically integrate with the actual recharge provider's API
        // For now, we'll just simulate a successful recharge
        $recharge->update([
            'status' => 'completed',
            'transaction_id' => 'TXN' . time(),
            'response_data' => [
                'message' => 'Recharge successful',
                'balance' => 'Updated balance information',
            ],
        ]);

        return redirect()->route('irecharge.index')
            ->with('success', 'Mobile recharge completed successfully');
    }
} 