<?php

namespace Modules\Irecharge\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Route;

class IrechargeServiceProvider extends ServiceProvider
{
    public function register()
    {
        //
    }

    public function boot()
    {
        $this->loadRoutesFrom(__DIR__ . '/../../routes/web.php');
        $this->loadViewsFrom(__DIR__ . '/../../resources/views', 'irecharge');
        $this->loadMigrationsFrom(__DIR__ . '/../../database/migrations');

        // Register module assets
        $this->publishes([
            __DIR__ . '/../../resources/js' => resource_path('js/modules/irecharge'),
        ], 'irecharge-assets');

        // Register module config
        $this->publishes([
            __DIR__ . '/../../config/irecharge.php' => config_path('irecharge.php'),
        ], 'irecharge-config');
    }
} 