<?php

namespace Modules\Irecharge\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MobileRecharge extends Model
{
    protected $fillable = [
        'user_id',
        'mobile_number',
        'provider',
        'amount',
        'status',
        'transaction_id',
        'response_data',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'response_data' => 'array',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
} 