import { Head, useForm } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';

interface BreadcrumbItem {
    title: string;
    href: string;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Mobile Recharge',
        href: '/irecharge',
    },
    {
        title: 'New Recharge',
        href: '/irecharge/create',
    },
];

const providers = [
    { name: 'Provider 1', code: 'provider1' },
    { name: 'Provider 2', code: 'provider2' },
];

export default function CreateMobileRecharge() {
    const form = useForm({
        mobile_number: '',
        provider: '',
        amount: '',
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        form.post(route('irecharge.store'), {
            onSuccess: () => {
                toast.success('Mobile recharge completed successfully');
            },
            onError: () => {
                toast.error('Failed to process mobile recharge');
            },
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="New Mobile Recharge" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    <Card>
                        <CardHeader>
                            <CardTitle>New Mobile Recharge</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <form onSubmit={handleSubmit} className="space-y-6">
                                <div className="space-y-2">
                                    <Label htmlFor="mobile_number">Mobile Number</Label>
                                    <Input
                                        id="mobile_number"
                                        type="tel"
                                        value={form.data.mobile_number}
                                        onChange={(e) => form.setData('mobile_number', e.target.value)}
                                        placeholder="Enter mobile number"
                                        required
                                    />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="provider">Provider</Label>
                                    <Select
                                        value={form.data.provider}
                                        onValueChange={(value) => form.setData('provider', value)}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select provider" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {providers.map((provider) => (
                                                <SelectItem key={provider.code} value={provider.code}>
                                                    {provider.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="amount">Amount</Label>
                                    <Input
                                        id="amount"
                                        type="number"
                                        min="1"
                                        step="0.01"
                                        value={form.data.amount}
                                        onChange={(e) => form.setData('amount', e.target.value)}
                                        placeholder="Enter amount"
                                        required
                                    />
                                </div>

                                <Button type="submit" disabled={form.processing}>
                                    {form.processing ? 'Processing...' : 'Process Recharge'}
                                </Button>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
} 