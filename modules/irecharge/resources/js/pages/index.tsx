import { Head } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { Link } from '@inertiajs/react';

interface BreadcrumbItem {
    title: string;
    href: string;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Mobile Recharge',
        href: '/irecharge',
    },
];

interface MobileRecharge {
    id: number;
    mobile_number: string;
    provider: string;
    amount: number;
    status: string;
    transaction_id: string | null;
    created_at: string;
}

interface Props {
    recharges: {
        data: MobileRecharge[];
        links: {
            first: string;
            last: string;
            prev: string | null;
            next: string | null;
        };
    };
}

export default function MobileRechargeIndex({ recharges }: Props) {
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Mobile Recharge" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center mb-6">
                        <h1 className="text-2xl font-semibold">Mobile Recharge</h1>
                        <Button asChild>
                            <Link href={route('irecharge.create')}>
                                <Plus className="mr-2 h-4 w-4" />
                                New Recharge
                            </Link>
                        </Button>
                    </div>

                    <Card>
                        <CardHeader>
                            <CardTitle>Recent Recharges</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {recharges.data.length === 0 ? (
                                    <p className="text-center text-muted-foreground py-4">
                                        No recharges found.
                                    </p>
                                ) : (
                                    recharges.data.map((recharge) => (
                                        <Card key={recharge.id} className="p-4">
                                            <div className="flex items-center justify-between">
                                                <div>
                                                    <h3 className="font-medium">
                                                        {recharge.mobile_number}
                                                    </h3>
                                                    <p className="text-sm text-muted-foreground">
                                                        Provider: {recharge.provider}
                                                    </p>
                                                    <p className="text-sm text-muted-foreground">
                                                        Amount: ${recharge.amount}
                                                    </p>
                                                </div>
                                                <div className="text-right">
                                                    <p className="text-sm font-medium">
                                                        {recharge.status}
                                                    </p>
                                                    {recharge.transaction_id && (
                                                        <p className="text-sm text-muted-foreground">
                                                            TXN: {recharge.transaction_id}
                                                        </p>
                                                    )}
                                                    <p className="text-sm text-muted-foreground">
                                                        {new Date(recharge.created_at).toLocaleString()}
                                                    </p>
                                                </div>
                                            </div>
                                        </Card>
                                    ))
                                )}
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
} 