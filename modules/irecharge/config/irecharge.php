<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Mobile Recharge Providers
    |--------------------------------------------------------------------------
    |
    | This array contains the list of mobile recharge providers and their
    | configuration settings.
    |
    */
    'providers' => [
        'provider1' => [
            'name' => 'Provider 1',
            'api_key' => env('PROVIDER1_API_KEY'),
            'api_secret' => env('PROVIDER1_API_SECRET'),
            'api_url' => env('PROVIDER1_API_URL'),
        ],
        'provider2' => [
            'name' => 'Provider 2',
            'api_key' => env('PROVIDER2_API_KEY'),
            'api_secret' => env('PROVIDER2_API_SECRET'),
            'api_url' => env('PROVIDER2_API_URL'),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Commission Settings
    |--------------------------------------------------------------------------
    |
    | This array contains the commission percentages for different user roles.
    |
    */
    'commission' => [
        'dealer' => 2.5,
        'retailer' => 2.0,
        'agent' => 1.5,
    ],

    /*
    |--------------------------------------------------------------------------
    | Transaction Settings
    |--------------------------------------------------------------------------
    |
    | This array contains the settings for transaction processing.
    |
    */
    'transaction' => [
        'timeout' => 30, // seconds
        'retry_attempts' => 3,
        'retry_delay' => 5, // seconds
    ],
]; 