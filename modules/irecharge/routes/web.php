<?php

use Illuminate\Support\Facades\Route;
use Modules\Irecharge\Http\Controllers\MobileRechargeController;

Route::middleware(['web', 'auth'])->group(function () {
    Route::prefix('irecharge')->name('irecharge.')->group(function () {
        Route::get('/', [MobileRechargeController::class, 'index'])->name('index');
        Route::get('/create', [MobileRechargeController::class, 'create'])->name('create');
        Route::post('/', [MobileRechargeController::class, 'store'])->name('store');
    });
}); 